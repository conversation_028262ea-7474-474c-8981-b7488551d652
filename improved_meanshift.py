#!/usr/bin/env python3
"""
Improved Mean Shift segmentation using ground truth for parameter optimization.
"""

import argparse
import numpy as np
import nibabel as nib
from pathlib import Path
from sklearn.cluster import MeanShift, estimate_bandwidth
from sklearn.metrics import adjusted_rand_score, normalized_mutual_info_score
from scipy.optimize import minimize
import json
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# Import functions from original meanshift_segment.py
from meanshift_segment import (
    load_nii, save_like, robust_norm, fit_meanshift, 
    relabel_by_center, discover_sample_dirs
)

class GroundTruthOptimizedMeanShift:
    """Mean Shift with ground truth-optimized parameters."""
    
    def __init__(self):
        self.optimal_params = {
            'brain_bandwidth': None,
            'nonbrain_bandwidth': None,
            'brain_quantile': 0.20,
            'nonbrain_quantile': 0.20,
            'sample_size': 5000
        }
        self.tissue_specific_params = {}
        
    def load_ground_truth_sample(self, case_dir: Path) -> <PERSON><PERSON>[np.ndarray, np.ndarray, np.ndarray]:
        """Load image, mask, and labels for a case."""
        img_path = case_dir / "image.nii.gz"
        mask_path = case_dir / "mask.nii.gz"
        labels_path = case_dir / "labels.nii.gz"
        
        if not all(p.exists() for p in [img_path, mask_path, labels_path]):
            raise FileNotFoundError(f"Missing files in {case_dir}")
            
        image, aff, hdr = load_nii(img_path)
        mask, _, _ = load_nii(mask_path)
        labels, _, _ = load_nii(labels_path)
        
        return image, mask > 0.5, labels.astype(int)
    
    def evaluate_segmentation_quality(self, pred_labels: np.ndarray, 
                                    gt_labels: np.ndarray, 
                                    mask: np.ndarray) -> Dict[str, float]:
        """Evaluate segmentation quality using multiple metrics."""
        # Focus on brain region only
        brain_mask = mask.flatten()
        pred_brain = pred_labels.flatten()[brain_mask]
        gt_brain = gt_labels.flatten()[brain_mask]
        
        # Remove background (label 0) from ground truth
        valid_mask = gt_brain > 0
        if valid_mask.sum() == 0:
            return {'ari': 0.0, 'nmi': 0.0, 'n_clusters': 0}
            
        pred_valid = pred_brain[valid_mask]
        gt_valid = gt_brain[valid_mask]
        
        # Calculate metrics
        ari = adjusted_rand_score(gt_valid, pred_valid)
        nmi = normalized_mutual_info_score(gt_valid, pred_valid)
        n_clusters = len(np.unique(pred_valid))
        
        return {
            'ari': ari,
            'nmi': nmi, 
            'n_clusters': n_clusters,
            'score': (ari + nmi) / 2  # Combined score
        }
    
    def optimize_bandwidth_for_case(self, image: np.ndarray, mask: np.ndarray, 
                                  gt_labels: np.ndarray) -> Dict[str, float]:
        """Optimize bandwidth parameters for a single case."""
        
        def objective(params):
            brain_bw, nonbrain_bw = params
            try:
                pred_labels = self.segment_with_params(
                    image, mask, brain_bw, nonbrain_bw
                )
                metrics = self.evaluate_segmentation_quality(pred_labels, gt_labels, mask)
                return -metrics['score']  # Minimize negative score
            except:
                return 1.0  # Bad score for failed segmentation
        
        # Search space for bandwidth
        bounds = [(0.01, 2.0), (0.01, 2.0)]  # brain_bw, nonbrain_bw
        
        # Try multiple starting points
        best_score = float('inf')
        best_params = None
        
        for brain_init in [0.1, 0.3, 0.5]:
            for nonbrain_init in [0.1, 0.3, 0.5]:
                try:
                    result = minimize(
                        objective, 
                        [brain_init, nonbrain_init],
                        bounds=bounds,
                        method='L-BFGS-B'
                    )
                    if result.fun < best_score:
                        best_score = result.fun
                        best_params = result.x
                except:
                    continue
        
        if best_params is None:
            return {'brain_bandwidth': 0.3, 'nonbrain_bandwidth': 0.3, 'score': 0.0}
            
        return {
            'brain_bandwidth': best_params[0],
            'nonbrain_bandwidth': best_params[1], 
            'score': -best_score
        }
    
    def segment_with_params(self, image: np.ndarray, mask: np.ndarray,
                          brain_bw: float, nonbrain_bw: float) -> np.ndarray:
        """Segment image with specific bandwidth parameters."""
        shp = image.shape
        img_flat = image.reshape(-1).astype(np.float32)
        m_flat = mask.reshape(-1)
        
        # Handle NaN/Inf
        finite = np.isfinite(img_flat)
        if not finite.all():
            med = np.nanmedian(img_flat)
            img_flat = np.nan_to_num(
                img_flat, nan=med,
                posinf=np.max(img_flat[finite]) if finite.any() else 0.0,
                neginf=np.min(img_flat[finite]) if finite.any() else 0.0
            )
        
        # Robust normalization
        z = np.zeros_like(img_flat, dtype=np.float32)
        if m_flat.any():
            z[m_flat] = robust_norm(img_flat[m_flat])
        if (~m_flat).any():
            z[~m_flat] = robust_norm(img_flat[~m_flat])
        
        labels_flat = np.zeros_like(img_flat, dtype=np.int32)
        n_brain = 0
        
        # Brain region with optimized bandwidth
        if m_flat.any():
            res_b = fit_meanshift(z[m_flat], bandwidth=brain_bw, 
                                quantile=0.2, sample_size=5000)
            if res_b is not None:
                lab_b, ctr_b = res_b
                lab_b = relabel_by_center(lab_b, ctr_b, base=0)
                labels_flat[m_flat] = lab_b
                n_brain = int(lab_b.max())
            else:
                labels_flat[m_flat] = 1
                n_brain = 1
        
        # Non-brain region with optimized bandwidth  
        if (~m_flat).any():
            res_nb = fit_meanshift(z[~m_flat], bandwidth=nonbrain_bw,
                                 quantile=0.2, sample_size=5000)
            if res_nb is not None:
                lab_nb, ctr_nb = res_nb
                lab_nb = relabel_by_center(lab_nb, ctr_nb, base=n_brain)
                labels_flat[~m_flat] = lab_nb
            else:
                labels_flat[~m_flat] = n_brain + 1
        
        return labels_flat.reshape(shp)
    
    def train_on_dataset(self, data_root: Path, max_samples: int = 20) -> Dict:
        """Train optimal parameters on a subset of the dataset."""
        print("Finding cases with ground truth labels...")
        
        # Find cases with labels
        cases_with_labels = []
        for case_dir in discover_sample_dirs(data_root, None):
            if (case_dir / "labels.nii.gz").exists():
                cases_with_labels.append(case_dir)
        
        print(f"Found {len(cases_with_labels)} cases with ground truth labels")
        
        if len(cases_with_labels) == 0:
            print("No ground truth labels found!")
            return self.optimal_params
        
        # Limit to max_samples for efficiency
        if len(cases_with_labels) > max_samples:
            cases_with_labels = cases_with_labels[:max_samples]
            print(f"Using first {max_samples} cases for training")
        
        all_results = []
        
        for i, case_dir in enumerate(cases_with_labels):
            print(f"Optimizing parameters for case {i+1}/{len(cases_with_labels)}: {case_dir.name}")
            
            try:
                image, mask, gt_labels = self.load_ground_truth_sample(case_dir)
                result = self.optimize_bandwidth_for_case(image, mask, gt_labels)
                result['case'] = case_dir.name
                all_results.append(result)
                print(f"  -> Brain BW: {result['brain_bandwidth']:.3f}, "
                      f"NonBrain BW: {result['nonbrain_bandwidth']:.3f}, "
                      f"Score: {result['score']:.3f}")
            except Exception as e:
                print(f"  -> Failed: {e}")
                continue
        
        if not all_results:
            print("No successful optimizations!")
            return self.optimal_params
        
        # Aggregate results
        brain_bws = [r['brain_bandwidth'] for r in all_results]
        nonbrain_bws = [r['nonbrain_bandwidth'] for r in all_results]
        scores = [r['score'] for r in all_results]
        
        # Use weighted average based on scores
        weights = np.array(scores)
        weights = weights - weights.min() + 0.1  # Ensure positive weights
        weights = weights / weights.sum()
        
        self.optimal_params.update({
            'brain_bandwidth': np.average(brain_bws, weights=weights),
            'nonbrain_bandwidth': np.average(nonbrain_bws, weights=weights),
            'training_cases': len(all_results),
            'avg_score': np.mean(scores),
            'case_results': all_results
        })
        
        print(f"\nOptimal parameters:")
        print(f"  Brain bandwidth: {self.optimal_params['brain_bandwidth']:.3f}")
        print(f"  Non-brain bandwidth: {self.optimal_params['nonbrain_bandwidth']:.3f}")
        print(f"  Average score: {self.optimal_params['avg_score']:.3f}")
        
        return self.optimal_params
    
    def save_params(self, filepath: Path):
        """Save optimized parameters to JSON file."""
        with open(filepath, 'w') as f:
            json.dump(self.optimal_params, f, indent=2)
        print(f"Saved optimized parameters to {filepath}")
    
    def load_params(self, filepath: Path):
        """Load optimized parameters from JSON file."""
        with open(filepath, 'r') as f:
            self.optimal_params.update(json.load(f))
        print(f"Loaded optimized parameters from {filepath}")


def main():
    parser = argparse.ArgumentParser(description="Optimize Mean Shift parameters using ground truth")
    parser.add_argument("--data_root", type=Path, required=True, 
                       help="Root directory of SynthStrip dataset")
    parser.add_argument("--output_params", type=Path, default="optimal_meanshift_params.json",
                       help="Output file for optimized parameters")
    parser.add_argument("--max_samples", type=int, default=20,
                       help="Maximum number of samples to use for optimization")
    parser.add_argument("--load_params", type=Path, default=None,
                       help="Load existing parameters instead of training")
    
    args = parser.parse_args()
    
    optimizer = GroundTruthOptimizedMeanShift()
    
    if args.load_params and args.load_params.exists():
        optimizer.load_params(args.load_params)
    else:
        optimizer.train_on_dataset(args.data_root, args.max_samples)
        optimizer.save_params(args.output_params)


if __name__ == "__main__":
    main()
