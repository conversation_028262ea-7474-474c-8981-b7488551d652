{"best_params": {}, "best_avg_ari": -1.0, "best_per_case": {}, "ms_sample": 3000, "mask_threshold": 0.5, "grid_tried_count": 6, "top5": [{"params": {"ms_bandwidth": null, "ms_quantile": 0.05, "spatial_w": 0.1, "intensity_w": 1.0, "grad_w": 0.0, "bin_seeding": false}, "avg_ari": -1.0, "per_case": {}}, {"params": {"ms_bandwidth": null, "ms_quantile": 0.05, "spatial_w": 0.25, "intensity_w": 1.0, "grad_w": 0.0, "bin_seeding": false}, "avg_ari": -1.0, "per_case": {}}, {"params": {"ms_bandwidth": 0.25, "ms_quantile": 0.05, "spatial_w": 0.1, "intensity_w": 1.0, "grad_w": 0.0, "bin_seeding": false}, "avg_ari": -1.0, "per_case": {}}, {"params": {"ms_bandwidth": 0.25, "ms_quantile": 0.05, "spatial_w": 0.25, "intensity_w": 1.0, "grad_w": 0.0, "bin_seeding": false}, "avg_ari": -1.0, "per_case": {}}, {"params": {"ms_bandwidth": 0.35, "ms_quantile": 0.05, "spatial_w": 0.1, "intensity_w": 1.0, "grad_w": 0.0, "bin_seeding": false}, "avg_ari": -1.0, "per_case": {}}]}