#!/usr/bin/env python3
# Mean Shift-based two-phase segmentation over a dataset with per-sample masks.
# For each case folder containing image.nii.gz and mask.nii.gz:
#   1) Normalize intensities robustly (separately for mask==True and mask==False).
#   2) Run Mean Shift clustering on brain voxels and non-brain voxels separately.
#   3) Remap cluster ids to contiguous labels:
#        Brain components     : 1 .. N_brain
#        Non-brain components : N_brain+1 .. N_brain+N_bg
#   4) Save labels.nii.gz under the mirrored case in --dst.
#   5) Write a case-local mapping CSV: dst/<case>/config/mapping_ms.csv

import argparse, csv, sys, shutil
from pathlib import Path
from typing import Tuple

import numpy as np
import nibabel as nib

from sklearn.cluster import MeanShift, estimate_bandwidth

# ------------------- constants -------------------

IMAGE_FILENAME = "image.nii.gz"
MASK_FILENAME  = "mask.nii.gz"
TRUTH_FILENAME = "labels.nii.gz"

SEED = 612385

# ------------------- IO helpers -------------------

def load_nii(p: Path):
    img = nib.load(str(p))
    return img.get_fdata(dtype=np.float32), img.affine, img.header

def save_like(arr: np.ndarray, affine, header, out_path: Path, dtype=np.uint16):
    out_path.parent.mkdir(parents=True, exist_ok=True)
    nib.save(nib.Nifti1Image(arr.astype(dtype), affine, header), str(out_path))

def discover_sample_dirs(root: Path, prefix: str | None):
    """Find immediate subfolders that contain required image & mask files."""
    out = []
    for p in sorted([x for x in root.iterdir() if x.is_dir()]):
        if prefix and (prefix not in p.name):
            continue
        if (p / IMAGE_FILENAME).exists() and (p / MASK_FILENAME).exists():
            out.append(p)
    return out

# ------------------- normalization -------------------

def robust_norm(vals: np.ndarray):
    v = vals.astype(np.float32)
    if v.size == 0:
        return v
    p25, p50, p75 = np.percentile(v, [25, 50, 75])
    scale = (p75 - p25) + 1e-6
    return (v - p50) / scale

# ------------------- Mean Shift helpers -------------------

def fit_meanshift(x: np.ndarray,
                  bandwidth: float | None,
                  quantile: float,
                  sample_size: int,
                  max_iter: int = 300) -> Tuple[np.ndarray, np.ndarray] | None:
    """
    x: 1D array of intensities (will be reshaped to (-1,1)).
    Returns (labels, centers) or None if not enough variance/data.
    """
    x = x.reshape(-1, 1).astype(np.float32)
    n = x.shape[0]
    if n == 0:
        return None
    if float(np.var(x)) < 1e-10:
        return None

    bw = bandwidth
    if bw is None:
        # estimate bandwidth using a subsample (for speed & robustness)
        n_samp = min(sample_size, n)
        try:
            bw = estimate_bandwidth(x, quantile=quantile, n_samples=n_samp, random_state=SEED)
        except Exception:
            bw = None
        # if estimate failed or tiny, let MeanShift pick (bw=None)
        if (bw is not None) and (bw < 1e-6):
            bw = None

    ms = MeanShift(bandwidth=bw, bin_seeding=True, max_iter=max_iter)
    ms.fit(x)
    labels = ms.labels_
    centers = ms.cluster_centers_.reshape(-1)
    return labels, centers

def relabel_by_center(labels: np.ndarray, centers: np.ndarray, base: int) -> np.ndarray:
    """
    Make labels contiguous starting at base+1, ordered by ascending cluster center.
    Example: centers order [0.1, 0.5, 1.2] -> map old_label_of_0.1 -> base+1, etc.
    """
    uniq = np.unique(labels)
    # map old label -> order index by center value
    order = np.argsort([centers[k] for k in uniq])
    mapping = {int(uniq[i]): (base + 1 + i) for i in order}
    return np.vectorize(lambda t: mapping[int(t)])(labels)

# ------------------- core -------------------

def two_phase_ms_labels(image: np.ndarray,
                        mask_bool: np.ndarray,
                        bandwidth: float | None,
                        quantile: float,
                        sample_size: int) -> Tuple[np.ndarray, int, int]:
    """
    Returns: (labels_volume, n_brain, n_bg)
      Brain components     : 1 .. n_brain
      Non-brain components : n_brain+1 .. n_brain+n_bg
    """
    shp = image.shape
    img_flat = image.reshape(-1).astype(np.float32)
    m_flat   = mask_bool.reshape(-1)

    # handle NaN/Inf
    finite = np.isfinite(img_flat)
    if not finite.all():
        med = np.nanmedian(img_flat)
        img_flat = np.nan_to_num(
            img_flat, nan=med,
            posinf=np.max(img_flat[finite]) if finite.any() else 0.0,
            neginf=np.min(img_flat[finite]) if finite.any() else 0.0
        )

    # robust, separate normalization per region
    z = np.zeros_like(img_flat, dtype=np.float32)
    if m_flat.any():
        z[m_flat]  = robust_norm(img_flat[m_flat])
    if (~m_flat).any():
        z[~m_flat] = robust_norm(img_flat[~m_flat])

    labels_flat = np.zeros_like(img_flat, dtype=np.int32)
    n_brain = 0
    n_bg = 0

    # brain region
    if m_flat.any():
        res_b = fit_meanshift(z[m_flat], bandwidth=bandwidth, quantile=quantile, sample_size=sample_size)
        if res_b is not None:
            lab_b, ctr_b = res_b
            lab_b = relabel_by_center(lab_b, ctr_b, base=0)  # => 1..n_brain
            labels_flat[m_flat] = lab_b
            n_brain = int(lab_b.max())
        else:
            labels_flat[m_flat] = 1
            n_brain = 1

    # non-brain region
    if (~m_flat).any():
        res_nb = fit_meanshift(z[~m_flat], bandwidth=bandwidth, quantile=quantile, sample_size=sample_size)
        if res_nb is not None:
            lab_nb, ctr_nb = res_nb
            lab_nb = relabel_by_center(lab_nb, ctr_nb, base=n_brain)  # => n_brain+1..n_brain+n_bg
            labels_flat[~m_flat] = lab_nb
            n_bg = int(lab_nb.max()) - n_brain
        else:
            labels_flat[~m_flat] = n_brain + 1
            n_bg = 1

    return labels_flat.reshape(shp), n_brain, n_bg

def write_case_mapping_csv(n_brain: int, n_bg: int, out_csv: Path):
    """Columns: label, mapping, class_name. mapping: 1=brain, 0=non-brain."""
    out_csv.parent.mkdir(parents=True, exist_ok=True)
    with open(out_csv, "w", newline="") as f:
        w = csv.writer(f)
        w.writerow(["label", "mapping", "class_name"])
        for lab in range(1, n_brain + 1):
            w.writerow([lab, 1, f"brain_ms_{lab}"])
        for j in range(1, n_bg + 1):
            lab = n_brain + j
            w.writerow([lab, 0, f"nonbrain_ms_{j}"])

def process_case(case_dir: Path, dst_root: Path,
                 bandwidth: float | None,
                 quantile: float,
                 sample_size: int,
                 mask_threshold: float,
                 copy_inputs: bool):
    img_p = case_dir / IMAGE_FILENAME
    msk_p = case_dir / MASK_FILENAME
    if not img_p.exists() or not msk_p.exists():
        print(f"[skip] missing image or mask in: {case_dir}", file=sys.stderr)
        return False, 0, 0

    image, aff, hdr = load_nii(img_p)
    mask,  _,  _    = load_nii(msk_p)
    mask_bool = mask > mask_threshold

    labels, n_brain, n_bg = two_phase_ms_labels(
        image=image,
        mask_bool=mask_bool,
        bandwidth=bandwidth,
        quantile=quantile,
        sample_size=sample_size,
    )

    dst_case = dst_root / case_dir.name
    dst_case.mkdir(parents=True, exist_ok=True)

    save_like(labels, aff, hdr, dst_case / TRUTH_FILENAME, dtype=np.uint16)

    # per-case mapping
    write_case_mapping_csv(n_brain, n_bg, dst_case / "config" / "mapping_ms.csv")

    if copy_inputs:
        shutil.copy2(img_p, dst_case / IMAGE_FILENAME)
        shutil.copy2(msk_p, dst_case / MASK_FILENAME)

    return True, n_brain, n_bg

# ------------------- CLI -------------------

def main():
    ap = argparse.ArgumentParser(description="Two-phase Mean Shift over dataset (per-sample masks).")
    ap.add_argument("--src", required=True, type=Path, help="Dataset root containing sample folders")
    ap.add_argument("--dst", required=True, type=Path, help="Output root; mirrors sample folders")
    ap.add_argument("--prefix", default=None, help="Only process folders whose name contains this string (e.g., 'fsm_t1')")
    ap.add_argument("--copy_inputs", action="store_true", help="Also copy image/mask into --dst case folders")
    ap.add_argument("--mask_threshold", type=float, default=0.5, help="Mask > threshold is considered inside (default 0.5)")

    # Mean Shift params
    ap.add_argument("--ms_bandwidth", type=float, default=None, help="Explicit bandwidth. If omitted, use estimate_bandwidth.")
    ap.add_argument("--ms_quantile", type=float, default=0.20, help="Quantile for estimate_bandwidth (ignored if --ms_bandwidth set).")
    ap.add_argument("--ms_sample",   type=int,   default=5000,  help="Max samples for estimate_bandwidth (speed/robustness).")

    args = ap.parse_args()

    cases = discover_sample_dirs(args.src, args.prefix)
    if not cases:
        print(f"No sample folders found under {args.src} (prefix={args.prefix})", file=sys.stderr)
        sys.exit(2)

    args.dst.mkdir(parents=True, exist_ok=True)

    n_ok = 0
    total_brain = 0
    total_bg = 0

    for case in cases:
        ok, nb, ng = process_case(
            case_dir=case,
            dst_root=args.dst,
            bandwidth=args.ms_bandwidth,
            quantile=args.ms_quantile,
            sample_size=args.ms_sample,
            mask_threshold=args.mask_threshold,
            copy_inputs=args.copy_inputs,
        )
        n_ok += int(ok)
        total_brain += nb
        total_bg += ng
        print(f"[{case.name}] clusters: brain={nb}, nonbrain={ng}")

    if n_ok == 0:
        print("No cases processed.", file=sys.stderr)
        sys.exit(3)

    print(f"\nProcessed {n_ok}/{len(cases)} cases.")
    if n_ok > 0:
        avg_b = total_brain / n_ok
        avg_nb = total_bg / n_ok
        print(f"Average clusters per case → brain: {avg_b:.2f}, nonbrain: {avg_nb:.2f}")
        print("Per-case mapping CSVs are under: <dst>/<case>/config/mapping_ms.csv")

# ------------------- Single-image Mean Shift brain mask (no external mask) -------------------

def meanshift_brain_mask(img_nii_path: str, out_labels_path: str,
                         bandwidth: float | None = None, quantile: float = 0.20, sample_size: int = 10000):
    """
    Simpler variant like your gmm_brain_mask(): cluster whole image with Mean Shift,
    pick cluster with *median* center (middle intensity) as brain, morph-free.
    """
    img_nii = nib.load(img_nii_path)
    img = img_nii.get_fdata().astype(np.float32)
    x = img.reshape(-1)

    # robust global normalization
    p1, p99 = np.percentile(x, [1, 99])
    x = np.clip((x - p1) / (p99 - p1 + 1e-6), 0, 1)

    res = fit_meanshift(x, bandwidth=bandwidth, quantile=quantile, sample_size=sample_size)
    if res is None:
        labels = np.zeros_like(x, dtype=np.uint8)
    else:
        lab, ctr = res
        # choose "middle" center as brain (heuristic, like your GMM version)
        order = np.argsort(ctr)
        mid_label = order[len(order)//2]
        labels = (lab == mid_label).astype(np.uint8)

    labels = labels.reshape(img.shape)
    nib.save(nib.Nifti1Image(labels, img_nii.affine, img_nii.header), out_labels_path)

if __name__ == "__main__":
    main()
