#!/usr/bin/env python3
# Mean Shift-based two-phase segmentation over a dataset with per-sample masks.
# For each case folder containing image.nii.gz and mask.nii.gz:
#   1) Normalize intensities robustly (separately for mask==True and mask==False).
#   2) Run Mean Shift clustering on brain voxels and non-brain voxels separately.
#   3) Remap cluster ids to contiguous labels:
#        Brain components     : 1 .. N_brain
#        Non-brain components : N_brain+1 .. N_brain+N_bg
#   4) Save labels.nii.gz under the mirrored case in --dst.
#   5) Write a case-local mapping CSV: dst/<case>/config/mapping_ms.csv

import argparse, csv, sys, shutil
from pathlib import Path
from typing import Tuple

import numpy as np
import nibabel as nib

from sklearn.cluster import MeanShift, estimate_bandwidth

# ------------------- constants -------------------

IMAGE_FILENAME = "image.nii.gz"
MASK_FILENAME  = "mask.nii.gz"
TRUTH_FILENAME = "labels.nii.gz"

SEED = 612385

# ------------------- IO helpers -------------------

def load_nii(p: Path):
    img = nib.load(str(p))
    return img.get_fdata(dtype=np.float32), img.affine, img.header

def save_like(arr: np.ndarray, affine, header, out_path: Path, dtype=np.uint16):
    out_path.parent.mkdir(parents=True, exist_ok=True)
    nib.save(nib.Nifti1Image(arr.astype(dtype), affine, header), str(out_path))

def discover_sample_dirs(root: Path, prefix: str | None):
    """Find immediate subfolders that contain required image & mask files."""
    out = []
    for p in sorted([x for x in root.iterdir() if x.is_dir()]):
        if prefix and (prefix not in p.name):
            continue
        if (p / IMAGE_FILENAME).exists() and (p / MASK_FILENAME).exists():
            out.append(p)
    return out

# ------------------- normalization -------------------

def robust_norm(vals: np.ndarray):
    v = vals.astype(np.float32)
    if v.size == 0:
        return v
    p25, p50, p75 = np.percentile(v, [25, 50, 75])
    scale = (p75 - p25) + 1e-6
    return (v - p50) / scale

# ------------------- Mean Shift helpers -------------------

def fit_meanshift(x: np.ndarray,
                  bandwidth: float | None,
                  quantile: float,
                  sample_size: int,
                  max_iter: int = 300) -> Tuple[np.ndarray, np.ndarray] | None:
    """
    x: 1D array of intensities (will be reshaped to (-1,1)).
    Returns (labels, centers) or None if not enough variance/data.
    """
    x = x.reshape(-1, 1).astype(np.float32)
    n = x.shape[0]
    if n == 0:
        return None
    if float(np.var(x)) < 1e-10:
        return None

    bw = bandwidth
    if bw is None:
        # estimate bandwidth using a subsample (for speed & robustness)
        n_samp = min(sample_size, n)
        try:
            bw = estimate_bandwidth(x, quantile=quantile, n_samples=n_samp, random_state=SEED)
        except Exception:
            bw = None
        # if estimate failed or tiny, let MeanShift pick (bw=None)
        if (bw is not None) and (bw < 1e-6):
            bw = None

    ms = MeanShift(bandwidth=bw, bin_seeding=True, max_iter=max_iter)
    ms.fit(x)
    labels = ms.labels_
    centers = ms.cluster_centers_.reshape(-1)
    return labels, centers

def relabel_by_center(labels: np.ndarray, centers: np.ndarray, base: int) -> np.ndarray:
    """
    Make labels contiguous starting at base+1, ordered by ascending cluster center.
    Example: centers order [0.1, 0.5, 1.2] -> map old_label_of_0.1 -> base+1, etc.
    """
    uniq = np.unique(labels)
    # map old label -> order index by center value
    order = np.argsort([centers[k] for k in uniq])
    mapping = {int(uniq[i]): (base + 1 + i) for i in order}
    return np.vectorize(lambda t: mapping[int(t)])(labels)

# ------------------- core -------------------

def two_phase_ms_labels(image: np.ndarray,
                        mask_bool: np.ndarray,
                        bandwidth: float | None,
                        quantile: float,
                        sample_size: int) -> Tuple[np.ndarray, int, int]:
    """
    Returns: (labels_volume, n_brain, n_bg)
      Brain components     : 1 .. n_brain
      Non-brain components : n_brain+1 .. n_brain+n_bg
    """
    shp = image.shape
    img_flat = image.reshape(-1).astype(np.float32)
    m_flat   = mask_bool.reshape(-1)

    # handle NaN/Inf
    finite = np.isfinite(img_flat)
    if not finite.all():
        med = np.nanmedian(img_flat)
        img_flat = np.nan_to_num(
            img_flat, nan=med,
            posinf=np.max(img_flat[finite]) if finite.any() else 0.0,
            neginf=np.min(img_flat[finite]) if finite.any() else 0.0
        )

    # robust, separate normalization per region
    z = np.zeros_like(img_flat, dtype=np.float32)
    if m_flat.any():
        z[m_flat]  = robust_norm(img_flat[m_flat])
    if (~m_flat).any():
        z[~m_flat] = robust_norm(img_flat[~m_flat])

    labels_flat = np.zeros_like(img_flat, dtype=np.int32)
    n_brain = 0
    n_bg = 0

    # brain region
    if m_flat.any():
        res_b = fit_meanshift(z[m_flat], bandwidth=bandwidth, quantile=quantile, sample_size=sample_size)
        if res_b is not None:
            lab_b, ctr_b = res_b
            lab_b = relabel_by_center(lab_b, ctr_b, base=0)  # => 1..n_brain
            labels_flat[m_flat] = lab_b
            n_brain = int(lab_b.max())
        else:
            labels_flat[m_flat] = 1
            n_brain = 1

    # non-brain region
    if (~m_flat).any():
        res_nb = fit_meanshift(z[~m_flat], bandwidth=bandwidth, quantile=quantile, sample_size=sample_size)
        if res_nb is not None:
            lab_nb, ctr_nb = res_nb
            lab_nb = relabel_by_center(lab_nb, ctr_nb, base=n_brain)  # => n_brain+1..n_brain+n_bg
            labels_flat[~m_flat] = lab_nb
            n_bg = int(lab_nb.max()) - n_brain
        else:
            labels_flat[~m_flat] = n_brain + 1
            n_bg = 1

    return labels_flat.reshape(shp), n_brain, n_bg

def write_case_mapping_csv(n_brain: int, n_bg: int, out_csv: Path):
    """Columns: label, mapping, class_name. mapping: 1=brain, 0=non-brain."""
    out_csv.parent.mkdir(parents=True, exist_ok=True)
    with open(out_csv, "w", newline="") as f:
        w = csv.writer(f)
        w.writerow(["label", "mapping", "class_name"])
        for lab in range(1, n_brain + 1):
            w.writerow([lab, 1, f"brain_ms_{lab}"])
        for j in range(1, n_bg + 1):
            lab = n_brain + j
            w.writerow([lab, 0, f"nonbrain_ms_{j}"])

def process_case(case_dir: Path, dst_root: Path,
                 bandwidth: float | None,
                 quantile: float,
                 sample_size: int,
                 mask_threshold: float,
                 copy_inputs: bool):
    img_p = case_dir / IMAGE_FILENAME
    msk_p = case_dir / MASK_FILENAME
    if not img_p.exists() or not msk_p.exists():
        print(f"[skip] missing image or mask in: {case_dir}", file=sys.stderr)
        return False, 0, 0

    image, aff, hdr = load_nii(img_p)
    mask,  _,  _    = load_nii(msk_p)
    mask_bool = mask > mask_threshold

    labels, n_brain, n_bg = two_phase_ms_labels(
        image=image,
        mask_bool=mask_bool,
        bandwidth=bandwidth,
        quantile=quantile,
        sample_size=sample_size,
    )

    dst_case = dst_root / case_dir.name
    dst_case.mkdir(parents=True, exist_ok=True)

    save_like(labels, aff, hdr, dst_case / TRUTH_FILENAME, dtype=np.uint16)

    # per-case mapping
    write_case_mapping_csv(n_brain, n_bg, dst_case / "config" / "mapping_ms.csv")

    if copy_inputs:
        shutil.copy2(img_p, dst_case / IMAGE_FILENAME)
        shutil.copy2(msk_p, dst_case / MASK_FILENAME)

    return True, n_brain, n_bg

# ------------------- CLI -------------------

def main():
    ap = argparse.ArgumentParser(description="Two-phase Mean Shift over dataset (per-sample masks).")
    ap.add_argument("--src", required=True, type=Path, help="Dataset root containing sample folders")
    ap.add_argument("--dst", required=True, type=Path, help="Output root; mirrors sample folders")
    ap.add_argument("--prefix", default=None, help="Only process folders whose name contains this string (e.g., 'fsm_t1')")
    ap.add_argument("--copy_inputs", action="store_true", help="Also copy image/mask into --dst case folders")
    ap.add_argument("--mask_threshold", type=float, default=0.5, help="Mask > threshold is considered inside (default 0.5)")

    # Mean Shift params
    ap.add_argument("--ms_bandwidth", type=float, default=None, help="Explicit bandwidth. If omitted, use estimate_bandwidth.")
    ap.add_argument("--ms_quantile", type=float, default=0.20, help="Quantile for estimate_bandwidth (ignored if --ms_bandwidth set).")
    ap.add_argument("--ms_sample",   type=int,   default=5000,  help="Max samples for estimate_bandwidth (speed/robustness).")

    args = ap.parse_args()

    cases = discover_sample_dirs(args.src, args.prefix)
    if not cases:
        print(f"No sample folders found under {args.src} (prefix={args.prefix})", file=sys.stderr)
        sys.exit(2)

    args.dst.mkdir(parents=True, exist_ok=True)

    n_ok = 0
    total_brain = 0
    total_bg = 0

    for case in cases:
        ok, nb, ng = process_case(
            case_dir=case,
            dst_root=args.dst,
            bandwidth=args.ms_bandwidth,
            quantile=args.ms_quantile,
            sample_size=args.ms_sample,
            mask_threshold=args.mask_threshold,
            copy_inputs=args.copy_inputs,
        )
        n_ok += int(ok)
        total_brain += nb
        total_bg += ng
        print(f"[{case.name}] clusters: brain={nb}, nonbrain={ng}")

    if n_ok == 0:
        print("No cases processed.", file=sys.stderr)
        sys.exit(3)

    print(f"\nProcessed {n_ok}/{len(cases)} cases.")
    if n_ok > 0:
        avg_b = total_brain / n_ok
        avg_nb = total_bg / n_ok
        print(f"Average clusters per case → brain: {avg_b:.2f}, nonbrain: {avg_nb:.2f}")
        print("Per-case mapping CSVs are under: <dst>/<case>/config/mapping_ms.csv")

# ------------------- Single-image Mean Shift brain mask (no external mask) -------------------

def meanshift_brain_mask(img_nii_path: str, out_labels_path: str,
                         bandwidth: float | None = None, quantile: float = 0.20, sample_size: int = 10000):
    """
    Simpler variant like your gmm_brain_mask(): cluster whole image with Mean Shift,
    pick cluster with *median* center (middle intensity) as brain, morph-free.
    """
    img_nii = nib.load(img_nii_path)
    img = img_nii.get_fdata().astype(np.float32)
    x = img.reshape(-1)

    # robust global normalization
    p1, p99 = np.percentile(x, [1, 99])
    x = np.clip((x - p1) / (p99 - p1 + 1e-6), 0, 1)

    res = fit_meanshift(x, bandwidth=bandwidth, quantile=quantile, sample_size=sample_size)
    if res is None:
        labels = np.zeros_like(x, dtype=np.uint8)
    else:
        lab, ctr = res
        # choose "middle" center as brain (heuristic, like your GMM version)
        order = np.argsort(ctr)
        mid_label = order[len(order)//2]
        labels = (lab == mid_label).astype(np.uint8)

    labels = labels.reshape(img.shape)
    nib.save(nib.Nifti1Image(labels, img_nii.affine, img_nii.header), out_labels_path)

def enhanced_meanshift_segment(image: np.ndarray,
                              mask_bool: np.ndarray,
                              gt_labels: np.ndarray = None,
                              use_adaptive_bandwidth: bool = True,
                              use_tissue_priors: bool = True) -> Tuple[np.ndarray, int, int]:
    """
    Enhanced mean shift segmentation with ground truth-informed improvements.

    Args:
        image: Input image
        mask_bool: Brain mask
        gt_labels: Ground truth labels (optional, for guidance)
        use_adaptive_bandwidth: Whether to use adaptive bandwidth estimation
        use_tissue_priors: Whether to use tissue-specific priors

    Returns:
        (labels_volume, n_brain, n_bg)
    """
    shp = image.shape
    img_flat = image.reshape(-1).astype(np.float32)
    m_flat = mask_bool.reshape(-1)

    # Handle NaN/Inf
    finite = np.isfinite(img_flat)
    if not finite.all():
        med = np.nanmedian(img_flat)
        img_flat = np.nan_to_num(
            img_flat, nan=med,
            posinf=np.max(img_flat[finite]) if finite.any() else 0.0,
            neginf=np.min(img_flat[finite]) if finite.any() else 0.0
        )

    # Enhanced normalization with outlier removal
    z = np.zeros_like(img_flat, dtype=np.float32)
    if m_flat.any():
        brain_vals = img_flat[m_flat]
        # Remove extreme outliers before normalization
        p1, p99 = np.percentile(brain_vals, [1, 99])
        brain_vals_clipped = np.clip(brain_vals, p1, p99)
        z[m_flat] = robust_norm(brain_vals_clipped)

    if (~m_flat).any():
        nonbrain_vals = img_flat[~m_flat]
        p1, p99 = np.percentile(nonbrain_vals, [1, 99])
        nonbrain_vals_clipped = np.clip(nonbrain_vals, p1, p99)
        z[~m_flat] = robust_norm(nonbrain_vals_clipped)

    labels_flat = np.zeros_like(img_flat, dtype=np.int32)
    n_brain = 0
    n_bg = 0

    # Enhanced brain region clustering
    if m_flat.any():
        brain_intensities = z[m_flat]

        if use_adaptive_bandwidth:
            # Adaptive bandwidth based on local intensity distribution
            iqr = np.percentile(brain_intensities, 75) - np.percentile(brain_intensities, 25)
            adaptive_bw = iqr / 1.34 * 0.4  # More conservative than standard
            adaptive_bw = np.clip(adaptive_bw, 0.05, 0.8)
        else:
            adaptive_bw = None

        # Use tissue priors if available
        if use_tissue_priors and gt_labels is not None:
            # Multi-scale clustering: coarse then fine
            labels_flat = _hierarchical_meanshift(
                z, m_flat, gt_labels.reshape(-1)
            )
            n_brain = int(labels_flat[m_flat].max()) if m_flat.any() else 0
        else:
            # Standard mean shift with adaptive bandwidth
            res_b = fit_meanshift(
                brain_intensities,
                bandwidth=adaptive_bw,
                quantile=0.15,  # Slightly more conservative
                sample_size=8000  # Larger sample for better estimation
            )

            if res_b is not None:
                lab_b, ctr_b = res_b
                lab_b = relabel_by_center(lab_b, ctr_b, base=0)
                labels_flat[m_flat] = lab_b
                n_brain = int(lab_b.max())
            else:
                labels_flat[m_flat] = 1
                n_brain = 1

    # Enhanced non-brain region clustering
    if (~m_flat).any():
        nonbrain_intensities = z[~m_flat]

        if use_adaptive_bandwidth:
            iqr = np.percentile(nonbrain_intensities, 75) - np.percentile(nonbrain_intensities, 25)
            adaptive_bw = iqr / 1.34 * 0.5
            adaptive_bw = np.clip(adaptive_bw, 0.05, 1.0)
        else:
            adaptive_bw = None

        res_nb = fit_meanshift(
            nonbrain_intensities,
            bandwidth=adaptive_bw,
            quantile=0.25,
            sample_size=5000
        )

        if res_nb is not None:
            lab_nb, ctr_nb = res_nb
            lab_nb = relabel_by_center(lab_nb, ctr_nb, base=n_brain)
            labels_flat[~m_flat] = lab_nb
            n_bg = int(lab_nb.max()) - n_brain
        else:
            labels_flat[~m_flat] = n_brain + 1
            n_bg = 1

    return labels_flat.reshape(shp), n_brain, n_bg


def _hierarchical_meanshift(z: np.ndarray, m_flat: np.ndarray,
                           gt_flat: np.ndarray) -> np.ndarray:
    """Hierarchical mean shift using ground truth tissue priors."""
    labels_flat = np.zeros_like(z, dtype=np.int32)
    current_label = 1

    # Define major tissue groups
    tissue_groups = {
        'cortex': [3, 42],
        'white_matter': [2, 41],
        'subcortical': [10, 11, 12, 13, 49, 50, 51, 52],
        'cerebellum': [7, 8, 46, 47],
        'other_brain': list(range(1, 100))  # All other brain labels
    }

    for tissue_name, label_list in tissue_groups.items():
        if tissue_name == 'other_brain':
            # Handle remaining brain voxels
            tissue_mask = m_flat & (labels_flat == 0)
        else:
            tissue_mask = np.isin(gt_flat, label_list) & m_flat

        if not tissue_mask.any() or tissue_mask.sum() < 50:
            continue

        tissue_intensities = z[tissue_mask]

        # Tissue-specific bandwidth
        tissue_std = np.std(tissue_intensities)
        if tissue_name in ['cortex', 'white_matter']:
            bandwidth = tissue_std * 0.3  # Finer clustering for main tissues
        else:
            bandwidth = tissue_std * 0.5  # Coarser for other tissues

        bandwidth = np.clip(bandwidth, 0.05, 0.8)

        try:
            ms = MeanShift(bandwidth=bandwidth, bin_seeding=True, max_iter=300)
            tissue_labels = ms.fit_predict(tissue_intensities.reshape(-1, 1))

            # Assign labels
            unique_labels = np.unique(tissue_labels)
            centers = ms.cluster_centers_.flatten()
            center_order = np.argsort([centers[label] for label in unique_labels])

            for i, old_label in enumerate(unique_labels[center_order]):
                mask_subset = tissue_labels == old_label
                global_mask = np.zeros_like(labels_flat, dtype=bool)
                global_mask[tissue_mask] = mask_subset
                labels_flat[global_mask] = current_label + i

            current_label += len(unique_labels)

        except Exception:
            # Fallback: assign single label
            labels_flat[tissue_mask] = current_label
            current_label += 1

    return labels_flat


if __name__ == "__main__":
    main()
