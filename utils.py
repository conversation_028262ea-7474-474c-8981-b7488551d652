# utils.py
import numpy as np
import nibabel as nib
import csv
from pathlib import Path

IMAGE_FILENAME = "image.nii.gz"
MASK_FILENAME  = "mask.nii.gz"
TRUTH_FILENAME = "labels.nii.gz"

def load_nii(p: Path):
    img = nib.load(str(p))
    return img.get_fdata(dtype=np.float32), img.affine, img.header

def save_like(arr: np.ndarray, affine, header, out_path: Path, dtype=np.uint16):
    out_path.parent.mkdir(parents=True, exist_ok=True)
    nib.save(nib.Nifti1Image(arr.astype(dtype), affine, header), str(out_path))

def discover_sample_dirs(root: Path, prefix: str | None):
    return [
        p for p in sorted(root.iterdir())
        if p.is_dir()
        and (not prefix or prefix in p.name)
        and (p / IMAGE_FILENAME).exists()
        and (p / MASK_FILENAME).exists()
    ]

def robust_norm(v: np.ndarray):
    if v.size == 0:
        return v
    p25, p50, p75 = np.percentile(v, [25, 50, 75])
    return (v - p50) / ((p75 - p25) + 1e-6)

def write_case_mapping_csv(n_brain: int, n_bg: int, out_csv: Path, prefix="ms"):
    out_csv.parent.mkdir(parents=True, exist_ok=True)
    with open(out_csv, "w", newline="") as f:
        w = csv.writer(f)
        w.writerow(["label", "mapping", "class_name"])
        for lab in range(1, n_brain + 1):
            w.writerow([lab, 1, f"brain_{prefix}_{lab}"])
        for j in range(1, n_bg + 1):
            lab = n_brain + j
            w.writerow([lab, 0, f"nonbrain_{prefix}_{j}"])

def write_global_mapping_csv(K_brain: int, K_bg: int, out_csv: Path, prefix="gmm"):
    out_csv.parent.mkdir(parents=True, exist_ok=True)
    with open(out_csv, "w", newline="") as f:
        w = csv.writer(f)
        w.writerow(["label", "mapping", "class_name"])
        for lab in range(1, K_brain + 1):
            w.writerow([lab, 1, f"brain_{prefix}_{lab}"])
        for j in range(1, K_bg + 1):
            lab = K_brain + j
            w.writerow([lab, 0, f"nonbrain_{prefix}_{j}"])
