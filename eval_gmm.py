#!/usr/bin/env python3
# Evaluate which GT anatomical parts each predicted GMM component matches best.
# Matches cases by folder name across --gt_root and --pred_root.

import argparse, csv, sys
from pathlib import Path
import numpy as np
import nibabel as nib

LABELS_NAME = "labels.nii.gz"

# ---------------- IO ----------------

def load_nii_data(p: Path) -> np.ndarray:
    return nib.load(str(p)).get_fdata()

def read_mapping_csv(csv_path: Path):
    """
    Expect columns: label,mapping,class_name
      - mapping: 1=brain (anatomy), 0=non-brain/background
    Returns:
      lab2map  : dict[int->0/1]
      lab2name: dict[int->str] (fallback 'label_<id>')
    """
    lab2map, lab2name = {}, {}
    with open(csv_path, "r", newline="") as f:
        rdr = csv.reader(f)
        next(rdr, None)  # header
        for row in rdr:
            if not row:
                continue
            try:
                lab = int(row[0]); mp = int(row[1])
                name = row[2] if len(row) > 2 and row[2] != "" else f"label_{lab}"
                lab2map[lab] = 1 if mp == 1 else 0
                lab2name[lab] = name
            except Exception:
                # skip malformed rows
                pass
    return lab2map, lab2name

def intersect_cases(gt_root: Path, pred_root: Path, prefix: str | None):
    gt = {d.name: d for d in gt_root.iterdir() if d.is_dir()}
    pr = {d.name: d for d in pred_root.iterdir() if d.is_dir()}
    names = sorted(set(gt).intersection(pr))
    if prefix:
        names = [n for n in names if prefix in n]
    return [(gt[n], pr[n]) for n in names]

# ------------- core aggregation -------------

def add_counts_for_case(GT: np.ndarray,
                        PRED: np.ndarray,
                        gt_brain_ids: set[int],
                        pred_ids_all: set[int],
                        inter_counts: dict[tuple[int,int], int],
                        comp_total: dict[int, int],
                        comp_brain_total: dict[int, int],
                        gt_total: dict[int, int]):
    """
    Update:
      inter_counts[(comp, gt)] += |pred==comp & gt==gt|
      comp_total[comp]         += |pred==comp|
      comp_brain_total[comp]   += |pred==comp & gt in gt_brain_ids|
      gt_total[gt]             += |gt==gt|
    """
    # Update gt_total once per case
    uniq_g, cnt_g = np.unique(GT.astype(np.int64), return_counts=True)
    for g, c in zip(uniq_g, cnt_g):
        gt_total[int(g)] = gt_total.get(int(g), 0) + int(c)

    # Present predicted labels in this case (limit to ids we care about)
    uniq_p, _ = np.unique(PRED.astype(np.int64), return_counts=True)
    present = [int(l) for l in uniq_p if int(l) in pred_ids_all]

    # For each present component, accumulate overlap with GT labels
    for comp in present:
        mask_c = (PRED == comp)
        n_c = int(np.count_nonzero(mask_c))
        comp_total[comp] = comp_total.get(comp, 0) + n_c

        # GT distribution within this component
        gt_inside = GT[mask_c].astype(np.int64)
        ug, cg = np.unique(gt_inside, return_counts=True)
        brain_count = 0
        for g, c in zip(ug, cg):
            inter_counts[(comp, int(g))] = inter_counts.get((comp, int(g)), 0) + int(c)
            if int(g) in gt_brain_ids:
                brain_count += int(c)
        comp_brain_total[comp] = comp_brain_total.get(comp, 0) + brain_count

# ------------- metrics -------------

def dice(inter: int, a: int, b: int) -> float:
    return (2.0 * inter) / (a + b + 1e-8)

def iou(inter: int, a: int, b: int) -> float:
    return inter / (a + b - inter + 1e-8)

# ------------- pretty print -------------

def print_component_report(comp, cname, cmap, c_total, c_brain,
                           gt_brain_all, inter_counts, gt_total,
                           topk, gt_name, metric_name):
    purity = c_brain / (c_total + 1e-8)
    rows = []
    for g in gt_brain_all:
        inter = inter_counts.get((comp, g), 0)
        d = (2.0 * inter) / (c_total + gt_total.get(g, 0) + 1e-8)  # Dice
        j = inter / (c_total + gt_total.get(g, 0) - inter + 1e-8)  # IoU
        score = d if metric_name == "dice" else j
        rows.append((score, g, inter, d, j))
    rows.sort(key=lambda r: r[0], reverse=True)
    top = rows[:topk]

    print(f"\n=== Component {comp:>3} | {cname} | map={cmap} | vox={c_total:,} | brain%={purity*100:.1f} ===")
    print("   #   GT  Name                          Dice   IoU    Inter    Comp%    GT%")
    for i, (score, g, inter, d, j) in enumerate(top, 1):
        comp_pct = 100.0 * inter / (c_total + 1e-8)
        gt_count = gt_total.get(g, 0)
        gt_pct = 100.0 * inter / (gt_count + 1e-8) if gt_count else 0.0
        name = gt_name.get(g, f"label_{g}")[:28]
        print(f"  {i:>2}  {g:>4}  {name:<28}  {d:5.3f}  {j:5.3f}  {inter:8d}  {comp_pct:7.2f}%  {gt_pct:7.2f}%")

# ------------- main -------------

def main():
    ap = argparse.ArgumentParser(description="Match predicted GMM components to GT anatomy via Dice/IoU.")
    ap.add_argument("--gt_root", required=True, type=Path, help="GT root: <case>/labels.nii.gz")
    ap.add_argument("--pred_root", required=True, type=Path, help="Pred root: <case>/labels.nii.gz")
    ap.add_argument("--gt_mapping_csv", required=True, type=Path, help="GT mapping CSV (anatomy list with mapping=1)")
    ap.add_argument("--pred_mapping_csv", required=True, type=Path, help="Pred mapping CSV (components: brain=1 / non-brain=0)")
    ap.add_argument("--prefix", default=None, help="Filter cases by substring in folder name")
    ap.add_argument("--metric", choices=["dice","iou"], default="dice")
    ap.add_argument("--topk", type=int, default=5, help="Top-k GT classes to show per component")
    ap.add_argument("--include_nonbrain_components", action="store_true",
                    help="Also include predicted components with mapping=0 in the analysis")
    ap.add_argument("--long_csv", type=Path, default=None,
                    help="Save long-format rows: comp,comp_name,gt,gt_name,inter,comp_total,gt_total,metric")
    ap.add_argument("--matrix_csv", type=Path, default=None,
                    help="Save a wide matrix: rows=components, cols=GT classes, cell=metric")
    args = ap.parse_args()

    pairs = intersect_cases(args.gt_root, args.pred_root, args.prefix)
    if not pairs:
        print("No matching case folders between GT and Pred roots.", file=sys.stderr)
        sys.exit(2)

    gt_map, gt_name = read_mapping_csv(args.gt_mapping_csv)
    pr_map, pr_name = read_mapping_csv(args.pred_mapping_csv)

    gt_brain_ids = {lab for lab, m in gt_map.items() if m == 1}
    # Which predicted labels to analyze:
    pred_ids_all = set(pr_map.keys()) if args.include_nonbrain_components \
                   else {lab for lab, m in pr_map.items() if m == 1}

    inter_counts = {}       # (comp, gt) -> int
    comp_total = {}         # comp -> int
    comp_brain_total = {}   # comp -> int (overlap with any GT-brain)
    gt_total = {}           # gt -> int
    total_gt_brain = 0

    for gt_case, pr_case in pairs:
        gt_p = gt_case / LABELS_NAME
        pr_p = pr_case / LABELS_NAME
        if not (gt_p.exists() and pr_p.exists()):
            print(f"[skip] missing labels: {gt_case.name}", file=sys.stderr)
            continue
        GT = load_nii_data(gt_p)
        PRED = load_nii_data(pr_p)
        if GT.shape != PRED.shape:
            print(f"[skip] shape mismatch in {gt_case.name}: {GT.shape} vs {PRED.shape}", file=sys.stderr)
            continue

        add_counts_for_case(
            GT=GT, PRED=PRED,
            gt_brain_ids=gt_brain_ids,
            pred_ids_all=pred_ids_all,
            inter_counts=inter_counts,
            comp_total=comp_total,
            comp_brain_total=comp_brain_total,
            gt_total=gt_total
        )
        # accumulate total GT brain voxels (not used elsewhere, but kept for completeness)
        brain_mask = np.isin(GT.astype(np.int64), list(gt_brain_ids))
        total_gt_brain += int(np.count_nonzero(brain_mask))

    if not comp_total:
        print("No components found to analyze.", file=sys.stderr)
        sys.exit(3)

    # Build list of all GT brain classes encountered (for matrix header)
    gt_brain_all = sorted([g for g in gt_total.keys() if g in gt_brain_ids])

    # Long-format rows (optional)
    long_rows = []

    print(f"Aggregated over {len(pairs)} cases.")
    print("\nPer-component matches (top-{} by {}):".format(args.topk, args.metric.upper()))

    # Pretty per-component report + build long rows
    for comp in sorted(comp_total.keys()):
        c_total = comp_total[comp]
        c_brain = comp_brain_total.get(comp, 0)
        cname   = pr_name.get(comp, f"label_{comp}")
        cmap    = pr_map.get(comp, 0)

        print_component_report(
            comp=comp, cname=cname, cmap=cmap, c_total=c_total, c_brain=c_brain,
            gt_brain_all=gt_brain_all, inter_counts=inter_counts, gt_total=gt_total,
            topk=args.topk, gt_name=gt_name, metric_name=args.metric
        )

        if args.long_csv:
            for g in gt_brain_all:
                inter = inter_counts.get((comp, g), 0)
                if args.metric == "dice":
                    m = dice(inter, c_total, gt_total.get(g, 0))
                else:
                    m = iou(inter, c_total, gt_total.get(g, 0))
                long_rows.append({
                    "comp": comp,
                    "comp_name": cname,
                    "comp_mapping": cmap,
                    "gt": g,
                    "gt_name": gt_name.get(g, f"label_{g}"),
                    "inter": inter,
                    "comp_total": c_total,
                    "gt_total": gt_total.get(g, 0),
                    args.metric: m
                })

    # Write long-format CSV once
    if args.long_csv:
        args.long_csv.parent.mkdir(parents=True, exist_ok=True)
        flds = ["comp","comp_name","comp_mapping","gt","gt_name",
                "inter","comp_total","gt_total",args.metric]
        with open(args.long_csv, "w", newline="") as f:
            w = csv.DictWriter(f, fieldnames=flds)
            w.writeheader()
            w.writerows(long_rows)
        print(f"\nLong-format component↔anatomy rows saved to: {args.long_csv}")

    # Optional wide matrix CSV (components × GT classes)
    if args.matrix_csv:
        args.matrix_csv.parent.mkdir(parents=True, exist_ok=True)
        with open(args.matrix_csv, "w", newline="") as f:
            w = csv.writer(f)
            header = ["comp_id","comp_name","comp_mapping","comp_total","purity_brain"] + \
                     [f"{gt}::{gt_name.get(gt, f'label_{gt}')}" for gt in gt_brain_all]
            w.writerow(header)
            for comp in sorted(comp_total.keys()):
                c_total = comp_total[comp]
                purity = comp_brain_total.get(comp,0) / (c_total + 1e-8)
                row = [comp, pr_name.get(comp, f"label_{comp}"), pr_map.get(comp, 0),
                       c_total, purity]
                for g in gt_brain_all:
                    inter = inter_counts.get((comp, g), 0)
                    val = dice(inter, c_total, gt_total.get(g, 0)) if args.metric == "dice" \
                          else iou(inter, c_total, gt_total.get(g, 0))
                    row.append(val)
                w.writerow(row)
        print(f"Component×Anatomy {args.metric.upper()} matrix saved to: {args.matrix_csv}")

if __name__ == "__main__":
    main()
