#!/usr/bin/env python3
"""
ms_calibrate.py
Grid-search Mean Shift params against ground-truth labels (labels.nii.gz by default).
Uses meanshift_segment.two_phase_ms_labels so it matches your current pipeline.

Scoring: Adjusted Rand Index (label-permutation invariant).
Output: JSON with best params + summary.

Example (CMD):
  python -u ms_calibrate.py ^
    --src "C:\\...\\synthstrip_data_v1.5_2d" ^
    --prefix "asl_t1" ^
    --gt_name "labels.nii.gz" ^
    --out_json "C:\\...\\ms_preset_asl_t1.json"

Tip: start with a *small grid* so it runs fast, then expand.
"""

import argparse, json, sys, traceback
from pathlib import Path
from typing import List, Tuple, Dict, Any, Optional

import numpy as np
import nibabel as nib
from sklearn.metrics import adjusted_rand_score

# make sure we can import the local meanshift_segment
HERE = Path(__file__).resolve().parent
if str(HERE) not in sys.path:
    sys.path.insert(0, str(HERE))

try:
    import meanshift_segment as msseg  # must contain two_phase_ms_labels()
except Exception:
    print("[calib] ERROR: could not import meanshift_segment.py from this directory.", file=sys.stderr)
    raise

IMAGE_FILENAME = "image.nii.gz"
MASK_FILENAME  = "mask.nii.gz"

def load_nii_data(path: Path) -> np.ndarray:
    img = nib.load(str(path))
    return img.get_fdata(dtype=np.float32)

def discover_labeled_cases(root: Path, prefix: Optional[str], gt_name: str) -> List[Path]:
    out: List[Path] = []
    for p in sorted([x for x in root.iterdir() if x.is_dir()]):
        if prefix and (prefix not in p.name):
            continue
        if (p / IMAGE_FILENAME).exists() and (p / MASK_FILENAME).exists() and (p / gt_name).exists():
            out.append(p)
    return out

def parse_float_list(s: str) -> List[float]:
    return [float(x.strip()) for x in s.split(",") if x.strip() != ""]

def parse_optional_float_list(s: str) -> List[Optional[float]]:
    out: List[Optional[float]] = []
    for x in s.split(","):
        x = x.strip()
        if x.lower() == "none":
            out.append(None)
        elif x != "":
            out.append(float(x))
    return out

def parse_int_list(s: str) -> List[int]:
    return [int(x.strip()) for x in s.split(",") if x.strip() != ""]

def parse_bool_list(s: str) -> List[bool]:
    out: List[bool] = []
    for x in s.split(","):
        x = x.strip().lower()
        if x in ("1", "true", "t", "yes", "y"):
            out.append(True)
        elif x in ("0", "false", "f", "no", "n"):
            out.append(False)
    return out

def ari_score(pred: np.ndarray, gt: np.ndarray) -> float:
    """Compute Adjusted Rand Index (flattened)."""
    a = pred.reshape(-1)
    b = gt.reshape(-1)
    # handle any NaNs in gt
    finite = np.isfinite(b)
    if not finite.all():
        m = np.nanmedian(b[finite]) if finite.any() else 0.0
        b = np.nan_to_num(b, nan=m)
    return adjusted_rand_score(a, b)

def main():
    ap = argparse.ArgumentParser(description="Calibrate Mean Shift params using GT labels.")
    ap.add_argument("--src", required=True, type=Path, help="Dataset root containing sample folders")
    ap.add_argument("--prefix", default=None, help="Only include folders whose name contains this string (e.g., 'asl_t1')")
    ap.add_argument("--gt_name", default="labels.nii.gz", help="Ground truth filename inside each case folder")
    ap.add_argument("--out_json", required=True, type=Path, help="Where to write best preset JSON")

    # Base knobs (not gridded unless you also add to grids)
    ap.add_argument("--ms_sample", type=int, default=3000, help="n_samples used when estimating bandwidth")
    ap.add_argument("--mask_threshold", type=float, default=0.5, help="Mask > threshold considered inside")

    # Grids (comma-separated). Keep small at first!
    ap.add_argument("--grid_bandwidth",  default="None,0.25,0.35", help="e.g. 'None,0.25,0.35'  (None => estimate)")
    ap.add_argument("--grid_quantile",   default="0.05",           help="Used only when bandwidth=None")
    ap.add_argument("--grid_spatial_w",  default="0.10,0.25",      help="Spatial weight grid")
    ap.add_argument("--grid_intensity_w",default="1.0",            help="Intensity weight grid")
    ap.add_argument("--grid_grad_w",     default="0.0",            help="Gradient weight grid (0 disables)")
    ap.add_argument("--grid_bin_seeding",default="0",              help="0,1")

    ap.add_argument("--limit_cases", type=int, default=None, help="Use only first N labeled cases (speed)")

    args = ap.parse_args()

    print(f"[calib] src={args.src} prefix={args.prefix} gt={args.gt_name}", flush=True)

    cases = discover_labeled_cases(args.src, args.prefix, args.gt_name)
    if args.limit_cases is not None:
        cases = cases[: args.limit_cases]
    print(f"[calib] Found {len(cases)} labeled case(s).", flush=True)
    if not cases:
        print("[calib] No cases with the requested GT. Exiting.", file=sys.stderr)
        sys.exit(2)

    # Build grids
    grid_bandwidth   = parse_optional_float_list(args.grid_bandwidth)
    grid_quantile    = parse_float_list(args.grid_quantile)
    grid_spatial_w   = parse_float_list(args.grid_spatial_w)
    grid_intensity_w = parse_float_list(args.grid_intensity_w)
    grid_grad_w      = parse_float_list(args.grid_grad_w)
    grid_bin_seeding = parse_bool_list(args.grid_bin_seeding) or [False]

    # Cartesian product of all grid lists
    grid: List[Dict[str, Any]] = []
    for bw in grid_bandwidth:
        for q in grid_quantile:
            for sw in grid_spatial_w:
                for iw in grid_intensity_w:
                    for gw in grid_grad_w:
                        for bs in grid_bin_seeding:
                            grid.append(dict(ms_bandwidth=bw, ms_quantile=q,
                                             spatial_w=sw, intensity_w=iw,
                                             grad_w=gw, bin_seeding=bs))

    print(f"[calib] Grid size: {len(grid)} combos.", flush=True)

    # Evaluate
    best_avg = -1.0
    best_params: Dict[str, Any] = {}
    best_per_case: Dict[str, float] = {}
    all_results: List[Dict[str, Any]] = []

    for gi, params in enumerate(grid, start=1):
        print(f"\n[calib] === ({gi}/{len(grid)}) trying params: {params} ===", flush=True)

        per_case_scores: Dict[str, float] = {}
        ok_cases = 0
        for case_dir in cases:
            try:
                img = load_nii_data(case_dir / IMAGE_FILENAME)
                msk = load_nii_data(case_dir / MASK_FILENAME)
                gt  = load_nii_data(case_dir / args.gt_name)

                mask_bool = (msk > args.mask_threshold)

                # run your segmentation with these params
                pred, n_brain, n_bg = msseg.two_phase_ms_labels(
                    image=img,
                    mask_bool=mask_bool,
                    bandwidth=params["ms_bandwidth"],
                    quantile=params["ms_quantile"],
                    sample_size=args.ms_sample,
                    spatial_w=params["spatial_w"],
                    intensity_w=params["intensity_w"],
                    grad_w=params["grad_w"],
                    bin_seeding=params["bin_seeding"],
                )

                score = ari_score(pred, gt)
                per_case_scores[case_dir.name] = float(score)
                ok_cases += 1
                print(f"  [case {case_dir.name}] ARI={score:.4f}  (clusters brain={n_brain}, nonbrain={n_bg})", flush=True)

            except Exception as e:
                print(f"  [case {case_dir.name}] ERROR: {e}", flush=True)
                traceback.print_exc(limit=1)

        if ok_cases == 0:
            avg_score = -1.0
        else:
            avg_score = float(np.mean(list(per_case_scores.values())))
        print(f"[calib] Avg ARI over {ok_cases} case(s): {avg_score:.4f}", flush=True)

        all_results.append({
            "params": params,
            "avg_ari": avg_score,
            "per_case": per_case_scores
        })

        if avg_score > best_avg:
            best_avg = avg_score
            best_params = params
            best_per_case = per_case_scores

    # Sort top results
    top = sorted(all_results, key=lambda d: d["avg_ari"], reverse=True)[:5]
    print("\n[calib] Top results:")
    for r in top:
        print(f"  avg_ari={r['avg_ari']:.4f}  params={r['params']}", flush=True)

    # Write JSON
    args.out_json.parent.mkdir(parents=True, exist_ok=True)
    payload = {
        "best_params": best_params,
        "best_avg_ari": best_avg,
        "best_per_case": best_per_case,
        "ms_sample": args.ms_sample,
        "mask_threshold": args.mask_threshold,
        "grid_tried_count": len(all_results),
        "top5": top,
    }
    with open(args.out_json, "w") as f:
        json.dump(payload, f, indent=2)
    print(f"\n[calib] Wrote preset to: {args.out_json}", flush=True)

if __name__ == "__main__":
    main()
