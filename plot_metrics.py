#!/usr/bin/env python3
# plot_metrics.py
# Usage:
#   python plot_metrics.py --csv path/to/metrics.csv --outdir plots

import argparse
from pathlib import Path
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

def main():
    ap = argparse.ArgumentParser(description="Plot training loss and validation Dice from a CSV.")
    ap.add_argument("--csv", required=True, help="Path to CSV with columns: epoch,loss,val_loss,val_Dice")
    ap.add_argument("--outdir", default="plots", help="Directory to save figures")
    ap.add_argument("--dpi", type=int, default=150, help="Figure DPI")
    args = ap.parse_args()

    outdir = Path(args.outdir); outdir.mkdir(parents=True, exist_ok=True)

    # Read and clean
    df = pd.read_csv(args.csv)
    # Drop unnamed/empty columns that sometimes appear from extra commas
    df = df.loc[:, ~df.columns.astype(str).str.contains("^Unnamed")]
    # Trim whitespace from headers
    df.columns = [str(c).strip() for c in df.columns]

    # Fallbacks if headers are odd: try to detect first sensible columns
    def num(s): 
        return pd.to_numeric(s, errors="coerce")

    # Required: epoch and loss
    epoch = num(df["epoch"]) if "epoch" in df.columns else num(df.iloc[:,0])
    loss  = num(df["loss"])  if "loss"  in df.columns else num(df.iloc[:,1])

    # Optional columns
    val_loss = num(df["val_loss"]) if "val_loss" in df.columns else None
    val_dice = num(df["val_Dice"]) if "val_Dice" in df.columns else None

    # --- Plot 1: loss ---
    plt.figure()
    plt.plot(epoch, loss, label="train loss")
    if val_loss is not None and not np.all(np.isnan(val_loss)):
        plt.plot(epoch, val_loss, label="val loss")
    plt.xlabel("Epoch")
    plt.ylabel("Loss")
    plt.title("Loss vs Epoch")
    plt.legend()
    plt.grid(True, linestyle="--", linewidth=0.5, alpha=0.7)
    plt.tight_layout()
    plt.savefig(outdir / "loss.png", dpi=args.dpi)
    plt.close()

    # --- Plot 2: validation Dice ---
    if val_dice is not None and not np.all(np.isnan(val_dice)):
        plt.figure()
        plt.plot(epoch, val_dice, label="val Dice")
        # Mark the best Dice
        best_idx = int(val_dice.idxmax())
        plt.scatter([epoch.iloc[best_idx]], [val_dice.iloc[best_idx]])
        plt.annotate(f"max={val_dice.iloc[best_idx]:.4f} @ {int(epoch.iloc[best_idx])}",
                     (epoch.iloc[best_idx], val_dice.iloc[best_idx]),
                     xytext=(10,10), textcoords="offset points")
        plt.xlabel("Epoch")
        plt.ylabel("Dice")
        plt.title("Validation Dice vs Epoch")
        plt.legend()
        plt.grid(True, linestyle="--", linewidth=0.5, alpha=0.7)
        plt.tight_layout()
        plt.savefig(outdir / "val_dice.png", dpi=args.dpi)
        plt.close()

    print(f"Saved: {outdir/'loss.png'}")
    if val_dice is not None and not np.all(np.isnan(val_dice)):
        print(f"Saved: {outdir/'val_dice.png'}")

if __name__ == "__main__":
    main()
