#!/usr/bin/env python3
"""
Evaluate and compare different Mean Shift approaches using ground truth.
"""

import argparse
import numpy as np
import pandas as pd
from pathlib import Path
from sklearn.metrics import adjusted_rand_score, normalized_mutual_info_score, silhouette_score
from sklearn.metrics.cluster import contingency_matrix
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple
import json

from meanshift_segment import (
    load_nii, discover_sample_dirs, two_phase_ms_labels, enhanced_meanshift_segment
)
from improved_meanshift import GroundTruthOptimizedMeanShift
from tissue_specific_meanshift import TissueSpecificMeanShift


class MeanShiftEvaluator:
    """Comprehensive evaluation of Mean Shift segmentation approaches."""
    
    def __init__(self):
        self.results = []
        self.methods = {
            'original': self._segment_original,
            'enhanced': self._segment_enhanced,
            'optimized': self._segment_optimized,
            'tissue_specific': self._segment_tissue_specific
        }
        
        # Initialize optimized methods
        self.gt_optimizer = GroundTruthOptimizedMeanShift()
        self.tissue_ms = TissueSpecificMeanShift()
    
    def load_optimized_params(self, params_file: Path):
        """Load pre-optimized parameters."""
        if params_file.exists():
            self.gt_optimizer.load_params(params_file)
            print(f"Loaded optimized parameters from {params_file}")
        else:
            print(f"Warning: {params_file} not found, using default parameters")
    
    def load_tissue_params(self, params_file: Path):
        """Load tissue-specific parameters."""
        if params_file.exists():
            self.tissue_ms.load_params(params_file)
            print(f"Loaded tissue parameters from {params_file}")
        else:
            print(f"Warning: {params_file} not found, using default parameters")
    
    def _segment_original(self, image: np.ndarray, mask: np.ndarray, 
                         gt_labels: np.ndarray = None) -> np.ndarray:
        """Original mean shift segmentation."""
        labels, _, _ = two_phase_ms_labels(
            image=image,
            mask_bool=mask,
            bandwidth=None,
            quantile=0.20,
            sample_size=5000
        )
        return labels
    
    def _segment_enhanced(self, image: np.ndarray, mask: np.ndarray,
                         gt_labels: np.ndarray = None) -> np.ndarray:
        """Enhanced mean shift with adaptive bandwidth."""
        labels, _, _ = enhanced_meanshift_segment(
            image=image,
            mask_bool=mask,
            gt_labels=gt_labels,
            use_adaptive_bandwidth=True,
            use_tissue_priors=False
        )
        return labels
    
    def _segment_optimized(self, image: np.ndarray, mask: np.ndarray,
                          gt_labels: np.ndarray = None) -> np.ndarray:
        """Ground truth optimized mean shift."""
        params = self.gt_optimizer.optimal_params
        brain_bw = params.get('brain_bandwidth', 0.3)
        nonbrain_bw = params.get('nonbrain_bandwidth', 0.3)
        
        return self.gt_optimizer.segment_with_params(
            image, mask, brain_bw, nonbrain_bw
        )
    
    def _segment_tissue_specific(self, image: np.ndarray, mask: np.ndarray,
                                gt_labels: np.ndarray = None) -> np.ndarray:
        """Tissue-specific mean shift."""
        return self.tissue_ms.segment_with_tissue_awareness(
            image, mask, gt_labels
        )
    
    def calculate_metrics(self, pred_labels: np.ndarray, gt_labels: np.ndarray,
                         mask: np.ndarray, image: np.ndarray) -> Dict[str, float]:
        """Calculate comprehensive evaluation metrics."""
        # Focus on brain region
        brain_mask = mask.flatten()
        pred_brain = pred_labels.flatten()[brain_mask]
        gt_brain = gt_labels.flatten()[brain_mask]
        image_brain = image.flatten()[brain_mask]
        
        # Remove background from ground truth
        valid_mask = gt_brain > 0
        if valid_mask.sum() == 0:
            return {metric: 0.0 for metric in ['ari', 'nmi', 'homogeneity', 'completeness', 
                                             'silhouette', 'n_clusters', 'n_gt_clusters']}
        
        pred_valid = pred_brain[valid_mask]
        gt_valid = gt_brain[valid_mask]
        image_valid = image_brain[valid_mask]
        
        # Clustering metrics
        ari = adjusted_rand_score(gt_valid, pred_valid)
        nmi = normalized_mutual_info_score(gt_valid, pred_valid)
        
        # Homogeneity and completeness
        cm = contingency_matrix(gt_valid, pred_valid)
        homogeneity = self._homogeneity_score(cm)
        completeness = self._completeness_score(cm)
        
        # Silhouette score (measure of cluster quality)
        try:
            if len(np.unique(pred_valid)) > 1:
                silhouette = silhouette_score(image_valid.reshape(-1, 1), pred_valid)
            else:
                silhouette = 0.0
        except:
            silhouette = 0.0
        
        # Cluster counts
        n_clusters = len(np.unique(pred_valid))
        n_gt_clusters = len(np.unique(gt_valid))
        
        return {
            'ari': ari,
            'nmi': nmi,
            'homogeneity': homogeneity,
            'completeness': completeness,
            'silhouette': silhouette,
            'n_clusters': n_clusters,
            'n_gt_clusters': n_gt_clusters,
            'cluster_ratio': n_clusters / n_gt_clusters if n_gt_clusters > 0 else 0
        }
    
    def _homogeneity_score(self, contingency_matrix: np.ndarray) -> float:
        """Calculate homogeneity score from contingency matrix."""
        if contingency_matrix.sum() == 0:
            return 0.0
        
        # Entropy of clusters
        cluster_sizes = contingency_matrix.sum(axis=0)
        cluster_entropy = -np.sum((cluster_sizes / cluster_sizes.sum()) * 
                                 np.log(cluster_sizes / cluster_sizes.sum() + 1e-10))
        
        # Conditional entropy
        conditional_entropy = 0.0
        for i in range(contingency_matrix.shape[1]):
            if cluster_sizes[i] > 0:
                cluster_dist = contingency_matrix[:, i] / cluster_sizes[i]
                cluster_dist = cluster_dist[cluster_dist > 0]
                conditional_entropy += (cluster_sizes[i] / cluster_sizes.sum()) * \
                                     (-np.sum(cluster_dist * np.log(cluster_dist)))
        
        return 1 - (conditional_entropy / (cluster_entropy + 1e-10))
    
    def _completeness_score(self, contingency_matrix: np.ndarray) -> float:
        """Calculate completeness score from contingency matrix."""
        return self._homogeneity_score(contingency_matrix.T)
    
    def evaluate_case(self, case_dir: Path, methods: List[str] = None) -> Dict:
        """Evaluate all methods on a single case."""
        if methods is None:
            methods = list(self.methods.keys())
        
        # Load data
        try:
            image, aff, hdr = load_nii(case_dir / "image.nii.gz")
            mask, _, _ = load_nii(case_dir / "mask.nii.gz")
            gt_labels, _, _ = load_nii(case_dir / "labels.nii.gz")
            
            mask_bool = mask > 0.5
            gt_labels = gt_labels.astype(int)
            
        except Exception as e:
            print(f"Failed to load {case_dir}: {e}")
            return {}
        
        case_results = {'case': case_dir.name}
        
        for method_name in methods:
            if method_name not in self.methods:
                continue
                
            print(f"  Running {method_name}...")
            
            try:
                # Run segmentation
                pred_labels = self.methods[method_name](image, mask_bool, gt_labels)
                
                # Calculate metrics
                metrics = self.calculate_metrics(pred_labels, gt_labels, mask_bool, image)
                
                # Add method prefix to metric names
                for metric, value in metrics.items():
                    case_results[f"{method_name}_{metric}"] = value
                    
            except Exception as e:
                print(f"    Failed: {e}")
                # Add zero metrics for failed method
                for metric in ['ari', 'nmi', 'homogeneity', 'completeness', 'silhouette']:
                    case_results[f"{method_name}_{metric}"] = 0.0
        
        return case_results
    
    def evaluate_dataset(self, data_root: Path, max_cases: int = 20,
                        methods: List[str] = None) -> pd.DataFrame:
        """Evaluate all methods on dataset."""
        print("Finding cases with ground truth labels...")
        
        cases_with_labels = []
        for case_dir in discover_sample_dirs(data_root, None):
            if (case_dir / "labels.nii.gz").exists():
                cases_with_labels.append(case_dir)
        
        if len(cases_with_labels) == 0:
            print("No cases with ground truth found!")
            return pd.DataFrame()
        
        if len(cases_with_labels) > max_cases:
            cases_with_labels = cases_with_labels[:max_cases]
        
        print(f"Evaluating {len(cases_with_labels)} cases...")
        
        all_results = []
        for i, case_dir in enumerate(cases_with_labels):
            print(f"Case {i+1}/{len(cases_with_labels)}: {case_dir.name}")
            
            case_results = self.evaluate_case(case_dir, methods)
            if case_results:
                all_results.append(case_results)
        
        return pd.DataFrame(all_results)
    
    def generate_report(self, results_df: pd.DataFrame, output_dir: Path):
        """Generate comprehensive evaluation report."""
        output_dir.mkdir(exist_ok=True)
        
        # Save raw results
        results_df.to_csv(output_dir / "evaluation_results.csv", index=False)
        
        # Calculate summary statistics
        methods = [col.split('_')[0] for col in results_df.columns if '_ari' in col]
        metrics = ['ari', 'nmi', 'homogeneity', 'completeness', 'silhouette']
        
        summary_data = []
        for method in methods:
            for metric in metrics:
                col_name = f"{method}_{metric}"
                if col_name in results_df.columns:
                    values = results_df[col_name].dropna()
                    summary_data.append({
                        'method': method,
                        'metric': metric,
                        'mean': values.mean(),
                        'std': values.std(),
                        'median': values.median(),
                        'min': values.min(),
                        'max': values.max()
                    })
        
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_csv(output_dir / "summary_statistics.csv", index=False)
        
        # Create visualizations
        self._create_visualizations(results_df, methods, metrics, output_dir)
        
        print(f"Evaluation report saved to {output_dir}")
        return summary_df
    
    def _create_visualizations(self, results_df: pd.DataFrame, methods: List[str],
                              metrics: List[str], output_dir: Path):
        """Create evaluation visualizations."""
        # Method comparison boxplots
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        axes = axes.flatten()
        
        for i, metric in enumerate(metrics):
            if i >= len(axes):
                break
                
            data_for_plot = []
            labels_for_plot = []
            
            for method in methods:
                col_name = f"{method}_{metric}"
                if col_name in results_df.columns:
                    values = results_df[col_name].dropna()
                    data_for_plot.append(values)
                    labels_for_plot.append(method)
            
            if data_for_plot:
                axes[i].boxplot(data_for_plot, labels=labels_for_plot)
                axes[i].set_title(f'{metric.upper()} Comparison')
                axes[i].set_ylabel(metric.upper())
                axes[i].tick_params(axis='x', rotation=45)
        
        # Remove empty subplots
        for i in range(len(metrics), len(axes)):
            fig.delaxes(axes[i])
        
        plt.tight_layout()
        plt.savefig(output_dir / "method_comparison.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        print("Visualizations saved")


def main():
    parser = argparse.ArgumentParser(description="Evaluate Mean Shift segmentation methods")
    parser.add_argument("--data_root", type=Path, required=True,
                       help="Root directory of SynthStrip dataset")
    parser.add_argument("--output_dir", type=Path, default="evaluation_results",
                       help="Output directory for results")
    parser.add_argument("--max_cases", type=int, default=20,
                       help="Maximum number of cases to evaluate")
    parser.add_argument("--optimized_params", type=Path, default="optimal_meanshift_params.json",
                       help="File with optimized parameters")
    parser.add_argument("--tissue_params", type=Path, default="tissue_meanshift_params.json",
                       help="File with tissue parameters")
    parser.add_argument("--methods", nargs='+', 
                       choices=['original', 'enhanced', 'optimized', 'tissue_specific'],
                       default=['original', 'enhanced'],
                       help="Methods to evaluate")
    
    args = parser.parse_args()
    
    evaluator = MeanShiftEvaluator()
    
    # Load parameters if available
    if 'optimized' in args.methods:
        evaluator.load_optimized_params(args.optimized_params)
    if 'tissue_specific' in args.methods:
        evaluator.load_tissue_params(args.tissue_params)
    
    # Run evaluation
    results_df = evaluator.evaluate_dataset(args.data_root, args.max_cases, args.methods)
    
    if not results_df.empty:
        summary_df = evaluator.generate_report(results_df, args.output_dir)
        print("\nSummary Statistics:")
        print(summary_df.groupby(['method', 'metric'])['mean'].unstack())
    else:
        print("No results to evaluate!")


if __name__ == "__main__":
    main()
