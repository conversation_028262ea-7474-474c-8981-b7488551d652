import os, numpy as np, nibabel as nib
from sklearn.mixture import GaussianMixture
from scipy.ndimage import binary_opening, binary_closing, binary_fill_holes, label as cc_label

def gmm_brain_mask(img_nii_path, out_labels_path, K=3, seed=0):
    img_nii = nib.load(img_nii_path)
    img = img_nii.get_fdata().astype(np.float32)

    # normalize per-scan
    x = img.reshape(-1, 1)
    q1, q99 = np.percentile(x, [1, 99])
    x = np.clip((x - q1) / (q99 - q1 + 1e-6), 0, 1)

    # fit GMM on intensities
    gmm = GaussianMixture(n_components=K, covariance_type="full", random_state=seed)
    gmm.fit(x)
    z = gmm.predict(x).reshape(img.shape)

    # heuristic: choose component whose mean is closest to brain intensity (usually middle)
    means = gmm.means_.flatten()
    brain_k = np.argsort(means)[len(means)//2]  # middle mean
    mask = (z == brain_k)

    # cleanup: morphology + keep largest connected component
    mask = binary_opening(mask, iterations=1)
    mask = binary_closing(mask, iterations=2)
    mask = binary_fill_holes(mask)
    cc, n = cc_label(mask)
    if n > 0:
        sizes = np.bincount(cc.ravel())
        sizes[0] = 0
        mask = cc == np.argmax(sizes)

    labels = np.where(mask, 1, 0).astype(np.uint8)
    out_nii = nib.Nifti1Image(labels, img_nii.affine, img_nii.header)
    nib.save(out_nii, out_labels_path)

def process_case(case_dir, K_list=(2,3,4,5), pick="best"):
    img_p = os.path.join(case_dir, "image.nii.gz")
    candidates = []
    for K in K_list:
        out_p = os.path.join(case_dir, f"labels_gmmK{K}.nii.gz")
        gmm_brain_mask(img_p, out_p, K=K)
        candidates.append(out_p)
    # simple picker: prefer middle K; replace with BIC/AIC or val Dice if GT exists
    chosen = os.path.join(case_dir, f"labels_gmmK{K_list[len(K_list)//2]}.nii.gz")
    final = os.path.join(case_dir, "labels.nii.gz")
    os.replace(chosen, final)
    # optional: delete the other candidates
