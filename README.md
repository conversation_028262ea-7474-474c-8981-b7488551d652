# Label Maps-Based Synthesis Model Followed by Segmentation

This project provides a training and inference flow for a synthesis model followed by a segmentation model.
Separate scripts are provided for training and inference, with parameters saved to json files for easy tracking.

Both the synthesis and the U-Net models are utilizing the [Neurite](https://github.com/adalca/neurite) package.

## Prerequisites

This project requires the following Python packages:

- `random` (built-in)
- `glob` (built-in)
- `os` (built-in)
- `numpy`
- `nibabel`
- `pandas`
- `scipy` (version 1.10.1)
- `multiprocess` 
- `tqdm` 
- `jupyter_notebook` 
- `xlsxwriter` 
- `tensorflow` (version 2.13.0)
- `neurite` (version 0.2)
- `voxelmorph` (version 0.2)

## Training

Download the 2D dataset from here : [Synthstrip page](https://surfer.nmr.mgh.harvard.edu/docs/synthstrip/)

To perform training, run the script training_script.py from command line. Make sure to change the paths of the data
and log directories to match locations on your computer.  
For each experiment, a new folder will be created with new index to avoid override.

In the training folder, the following data is saved:
- config.json: training parameters
- data_split: folder that saves the data split. Data split is randomly generated according to the number of samples in the split. You may want to change it to use existing data split instead.
- evaluation.csv: during training, training and validation losses (Dice score in the case of Dice loss) are saved to this file by the end of each epoch.

## Inference and evaluation
To perform inference and evaluation, run the script inference_script.py.
The script picks the model with best validation results.

The following are saved during inferece script execution:
- An output directory with the test cases, their ground truth and network results
- inference_args.json file to track the inference parameters
- Excel file is saved with the evaluation results


