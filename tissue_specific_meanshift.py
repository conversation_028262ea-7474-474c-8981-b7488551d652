#!/usr/bin/env python3
"""
Tissue-specific Mean Shift that learns different parameters for different anatomical regions.
"""

import numpy as np
import nibabel as nib
from pathlib import Path
from sklearn.cluster import MeanShift
from collections import defaultdict
import json
from typing import Dict, List, Tuple
import argparse

from meanshift_segment import load_nii, robust_norm, discover_sample_dirs

class TissueSpecificMeanShift:
    """Mean Shift with tissue-specific parameters learned from ground truth."""
    
    def __init__(self):
        # FreeSurfer label groups
        self.tissue_groups = {
            'cortex': [3, 42],  # Left/Right Cerebral Cortex
            'white_matter': [2, 41],  # Left/Right Cerebral White Matter
            'subcortical': [10, 11, 12, 13, 49, 50, 51, 52],  # <PERSON>hala<PERSON>, Caudate, Putamen, Pallidum
            'cerebellum': [7, 8, 46, 47],  # Cerebellar white/gray matter
            'ventricles': [4, 5, 14, 15, 43, 44],  # Ventricles
            'hippocampus': [17, 53],  # Left/Right Hippocampus
            'amygdala': [18, 54],  # Left/Right Amygdala
            'brainstem': [16],  # Brain stem
            'csf': [24],  # CSF
            'nonbrain': [100, 101, 102, 103, 104, 105]  # Non-brain labels
        }
        
        self.tissue_params = {}
        self.global_stats = {}
    
    def analyze_tissue_intensities(self, data_root: Path, max_samples: int = 50):
        """Analyze intensity distributions for different tissue types."""
        print("Analyzing tissue intensity distributions...")
        
        # Find cases with labels
        cases_with_labels = []
        for case_dir in discover_sample_dirs(data_root, None):
            if (case_dir / "labels.nii.gz").exists():
                cases_with_labels.append(case_dir)
        
        if len(cases_with_labels) == 0:
            print("No ground truth labels found!")
            return
        
        if len(cases_with_labels) > max_samples:
            cases_with_labels = cases_with_labels[:max_samples]
        
        print(f"Analyzing {len(cases_with_labels)} cases...")
        
        # Collect intensity statistics per tissue type
        tissue_intensities = defaultdict(list)
        
        for i, case_dir in enumerate(cases_with_labels):
            print(f"Processing case {i+1}/{len(cases_with_labels)}: {case_dir.name}")
            
            try:
                image, aff, hdr = load_nii(case_dir / "image.nii.gz")
                mask, _, _ = load_nii(case_dir / "mask.nii.gz")
                labels, _, _ = load_nii(case_dir / "labels.nii.gz")
                
                # Normalize image
                mask_bool = mask > 0.5
                img_flat = image.reshape(-1).astype(np.float32)
                mask_flat = mask_bool.reshape(-1)
                labels_flat = labels.reshape(-1).astype(int)
                
                # Robust normalization within brain
                if mask_flat.any():
                    brain_intensities = img_flat[mask_flat]
                    brain_intensities = robust_norm(brain_intensities)
                    
                    # Collect intensities for each tissue group
                    for tissue_name, label_list in self.tissue_groups.items():
                        if tissue_name == 'nonbrain':
                            continue  # Skip non-brain for now
                            
                        tissue_mask = np.isin(labels_flat, label_list) & mask_flat
                        if tissue_mask.any():
                            tissue_vals = brain_intensities[tissue_mask[mask_flat]]
                            if len(tissue_vals) > 100:  # Minimum sample size
                                tissue_intensities[tissue_name].extend(tissue_vals)
                
            except Exception as e:
                print(f"  Failed: {e}")
                continue
        
        # Calculate statistics for each tissue type
        for tissue_name, intensities in tissue_intensities.items():
            if len(intensities) < 1000:  # Skip tissues with too few samples
                continue
                
            intensities = np.array(intensities)
            
            # Calculate bandwidth using various methods
            std_bw = np.std(intensities)
            iqr_bw = (np.percentile(intensities, 75) - np.percentile(intensities, 25)) / 1.34
            scott_bw = len(intensities) ** (-1/5) * std_bw
            
            self.tissue_params[tissue_name] = {
                'mean': float(np.mean(intensities)),
                'std': float(np.std(intensities)),
                'median': float(np.median(intensities)),
                'iqr': float(np.percentile(intensities, 75) - np.percentile(intensities, 25)),
                'bandwidth_std': float(std_bw * 0.5),  # Conservative bandwidth
                'bandwidth_iqr': float(iqr_bw * 0.5),
                'bandwidth_scott': float(scott_bw),
                'n_samples': len(intensities)
            }
            
            print(f"  {tissue_name}: mean={self.tissue_params[tissue_name]['mean']:.3f}, "
                  f"std={self.tissue_params[tissue_name]['std']:.3f}, "
                  f"bw={self.tissue_params[tissue_name]['bandwidth_iqr']:.3f}")
    
    def get_adaptive_bandwidth(self, intensities: np.ndarray, tissue_type: str = 'unknown') -> float:
        """Get adaptive bandwidth based on tissue type and local intensity distribution."""
        if len(intensities) < 10:
            return 0.3  # Default bandwidth
        
        # Local statistics
        local_std = np.std(intensities)
        local_iqr = np.percentile(intensities, 75) - np.percentile(intensities, 25)
        
        # Use tissue-specific parameters if available
        if tissue_type in self.tissue_params:
            tissue_params = self.tissue_params[tissue_type]
            # Blend local and global statistics
            global_bw = tissue_params['bandwidth_iqr']
            local_bw = local_iqr / 1.34 * 0.5
            bandwidth = 0.7 * global_bw + 0.3 * local_bw
        else:
            # Fall back to local estimation
            bandwidth = local_iqr / 1.34 * 0.5
        
        # Ensure reasonable bounds
        return np.clip(bandwidth, 0.05, 1.0)
    
    def segment_with_tissue_awareness(self, image: np.ndarray, mask: np.ndarray,
                                    prior_labels: np.ndarray = None) -> np.ndarray:
        """Segment using tissue-aware mean shift."""
        shp = image.shape
        img_flat = image.reshape(-1).astype(np.float32)
        mask_flat = mask.reshape(-1)
        
        # Handle NaN/Inf
        finite = np.isfinite(img_flat)
        if not finite.all():
            med = np.nanmedian(img_flat)
            img_flat = np.nan_to_num(
                img_flat, nan=med,
                posinf=np.max(img_flat[finite]) if finite.any() else 0.0,
                neginf=np.min(img_flat[finite]) if finite.any() else 0.0
            )
        
        # Normalize
        z = np.zeros_like(img_flat, dtype=np.float32)
        if mask_flat.any():
            z[mask_flat] = robust_norm(img_flat[mask_flat])
        if (~mask_flat).any():
            z[~mask_flat] = robust_norm(img_flat[~mask_flat])
        
        labels_flat = np.zeros_like(img_flat, dtype=np.int32)
        
        # Brain region with adaptive bandwidth
        if mask_flat.any():
            brain_intensities = z[mask_flat]
            
            # If we have prior knowledge about tissue types, use it
            if prior_labels is not None:
                # Segment different tissue types separately
                current_label = 1
                prior_flat = prior_labels.reshape(-1).astype(int)
                
                for tissue_name, label_list in self.tissue_groups.items():
                    if tissue_name == 'nonbrain':
                        continue
                    
                    tissue_mask = np.isin(prior_flat, label_list) & mask_flat
                    if not tissue_mask.any():
                        continue
                    
                    tissue_intensities = z[tissue_mask]
                    if len(tissue_intensities) < 10:
                        labels_flat[tissue_mask] = current_label
                        current_label += 1
                        continue
                    
                    # Use tissue-specific bandwidth
                    bandwidth = self.get_adaptive_bandwidth(tissue_intensities, tissue_name)
                    
                    try:
                        ms = MeanShift(bandwidth=bandwidth, bin_seeding=True, max_iter=300)
                        tissue_labels = ms.fit_predict(tissue_intensities.reshape(-1, 1))
                        
                        # Relabel to be contiguous
                        unique_labels = np.unique(tissue_labels)
                        for i, old_label in enumerate(unique_labels):
                            tissue_mask_subset = tissue_labels == old_label
                            global_mask = np.zeros_like(labels_flat, dtype=bool)
                            global_mask[tissue_mask] = tissue_mask_subset
                            labels_flat[global_mask] = current_label + i
                        
                        current_label += len(unique_labels)
                        
                    except Exception as e:
                        print(f"Failed to cluster {tissue_name}: {e}")
                        labels_flat[tissue_mask] = current_label
                        current_label += 1
            else:
                # Standard mean shift on entire brain
                bandwidth = self.get_adaptive_bandwidth(brain_intensities, 'cortex')  # Use cortex as default
                
                try:
                    ms = MeanShift(bandwidth=bandwidth, bin_seeding=True, max_iter=300)
                    brain_labels = ms.fit_predict(brain_intensities.reshape(-1, 1))
                    
                    # Sort by cluster centers
                    centers = ms.cluster_centers_.flatten()
                    unique_labels = np.unique(brain_labels)
                    center_order = np.argsort([centers[label] for label in unique_labels])
                    
                    for i, old_label in enumerate(unique_labels[center_order]):
                        labels_flat[mask_flat][brain_labels == old_label] = i + 1
                        
                except Exception as e:
                    print(f"Failed to cluster brain: {e}")
                    labels_flat[mask_flat] = 1
        
        # Non-brain region
        if (~mask_flat).any():
            nonbrain_intensities = z[~mask_flat]
            bandwidth = self.get_adaptive_bandwidth(nonbrain_intensities, 'nonbrain')
            
            try:
                ms = MeanShift(bandwidth=bandwidth, bin_seeding=True, max_iter=300)
                nonbrain_labels = ms.fit_predict(nonbrain_intensities.reshape(-1, 1))
                
                max_brain_label = labels_flat[mask_flat].max() if mask_flat.any() else 0
                unique_labels = np.unique(nonbrain_labels)
                
                for i, old_label in enumerate(unique_labels):
                    labels_flat[~mask_flat][nonbrain_labels == old_label] = max_brain_label + i + 1
                    
            except Exception as e:
                print(f"Failed to cluster non-brain: {e}")
                max_brain_label = labels_flat[mask_flat].max() if mask_flat.any() else 0
                labels_flat[~mask_flat] = max_brain_label + 1
        
        return labels_flat.reshape(shp)
    
    def save_params(self, filepath: Path):
        """Save tissue parameters to JSON file."""
        with open(filepath, 'w') as f:
            json.dump({
                'tissue_params': self.tissue_params,
                'tissue_groups': self.tissue_groups
            }, f, indent=2)
        print(f"Saved tissue parameters to {filepath}")
    
    def load_params(self, filepath: Path):
        """Load tissue parameters from JSON file."""
        with open(filepath, 'r') as f:
            data = json.load(f)
            self.tissue_params = data['tissue_params']
            if 'tissue_groups' in data:
                self.tissue_groups = data['tissue_groups']
        print(f"Loaded tissue parameters from {filepath}")


def main():
    parser = argparse.ArgumentParser(description="Analyze tissue-specific intensity distributions")
    parser.add_argument("--data_root", type=Path, required=True,
                       help="Root directory of SynthStrip dataset")
    parser.add_argument("--output_params", type=Path, default="tissue_meanshift_params.json",
                       help="Output file for tissue parameters")
    parser.add_argument("--max_samples", type=int, default=50,
                       help="Maximum number of samples to analyze")
    
    args = parser.parse_args()
    
    analyzer = TissueSpecificMeanShift()
    analyzer.analyze_tissue_intensities(args.data_root, args.max_samples)
    analyzer.save_params(args.output_params)


if __name__ == "__main__":
    main()
