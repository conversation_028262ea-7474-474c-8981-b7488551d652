#!/usr/bin/env python3
"""
Complete pipeline for improved Mean Shift segmentation using ground truth.
"""

import argparse
from pathlib import Path
import subprocess
import sys

def run_command(cmd, description):
    """Run a command and handle errors."""
    print(f"\n{'='*60}")
    print(f"STEP: {description}")
    print(f"{'='*60}")
    print(f"Running: {' '.join(map(str, cmd))}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        if result.stdout:
            print("Output:")
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        if e.stderr:
            print("Error output:")
            print(e.stderr)
        return False

def main():
    parser = argparse.ArgumentParser(description="Complete improved Mean Shift pipeline")
    parser.add_argument("--data_root", type=Path, required=True,
                       help="Root directory of SynthStrip dataset")
    parser.add_argument("--output_dir", type=Path, default="improved_meanshift_results",
                       help="Output directory for all results")
    parser.add_argument("--max_training_samples", type=int, default=20,
                       help="Maximum samples for parameter optimization")
    parser.add_argument("--max_tissue_samples", type=int, default=50,
                       help="Maximum samples for tissue analysis")
    parser.add_argument("--max_eval_samples", type=int, default=20,
                       help="Maximum samples for evaluation")
    parser.add_argument("--skip_training", action="store_true",
                       help="Skip parameter optimization (use existing files)")
    parser.add_argument("--skip_tissue_analysis", action="store_true",
                       help="Skip tissue analysis (use existing files)")
    parser.add_argument("--skip_evaluation", action="store_true",
                       help="Skip evaluation")
    
    args = parser.parse_args()
    
    # Create output directory
    args.output_dir.mkdir(exist_ok=True)
    
    # File paths
    optimized_params_file = args.output_dir / "optimal_meanshift_params.json"
    tissue_params_file = args.output_dir / "tissue_meanshift_params.json"
    evaluation_dir = args.output_dir / "evaluation"
    
    success_count = 0
    total_steps = 0
    
    # Step 1: Parameter optimization
    if not args.skip_training:
        total_steps += 1
        if not optimized_params_file.exists():
            cmd = [
                sys.executable, "improved_meanshift.py",
                "--data_root", str(args.data_root),
                "--output_params", str(optimized_params_file),
                "--max_samples", str(args.max_training_samples)
            ]
            
            if run_command(cmd, "Parameter Optimization"):
                success_count += 1
                print(f"✓ Optimized parameters saved to {optimized_params_file}")
            else:
                print(f"✗ Parameter optimization failed")
        else:
            print(f"✓ Using existing optimized parameters: {optimized_params_file}")
            success_count += 1
    
    # Step 2: Tissue analysis
    if not args.skip_tissue_analysis:
        total_steps += 1
        if not tissue_params_file.exists():
            cmd = [
                sys.executable, "tissue_specific_meanshift.py",
                "--data_root", str(args.data_root),
                "--output_params", str(tissue_params_file),
                "--max_samples", str(args.max_tissue_samples)
            ]
            
            if run_command(cmd, "Tissue-Specific Analysis"):
                success_count += 1
                print(f"✓ Tissue parameters saved to {tissue_params_file}")
            else:
                print(f"✗ Tissue analysis failed")
        else:
            print(f"✓ Using existing tissue parameters: {tissue_params_file}")
            success_count += 1
    
    # Step 3: Evaluation
    if not args.skip_evaluation:
        total_steps += 1
        
        # Determine which methods to evaluate based on available parameters
        methods = ["original", "enhanced"]
        if optimized_params_file.exists():
            methods.append("optimized")
        if tissue_params_file.exists():
            methods.append("tissue_specific")
        
        cmd = [
            sys.executable, "evaluate_meanshift.py",
            "--data_root", str(args.data_root),
            "--output_dir", str(evaluation_dir),
            "--max_cases", str(args.max_eval_samples),
            "--methods"] + methods
        
        if optimized_params_file.exists():
            cmd.extend(["--optimized_params", str(optimized_params_file)])
        if tissue_params_file.exists():
            cmd.extend(["--tissue_params", str(tissue_params_file)])
        
        if run_command(cmd, "Method Evaluation"):
            success_count += 1
            print(f"✓ Evaluation results saved to {evaluation_dir}")
        else:
            print(f"✗ Evaluation failed")
    
    # Step 4: Generate example segmentations
    total_steps += 1
    example_dir = args.output_dir / "examples"
    example_dir.mkdir(exist_ok=True)
    
    print(f"\n{'='*60}")
    print("STEP: Generate Example Segmentations")
    print(f"{'='*60}")
    
    try:
        # Find a case with ground truth
        from meanshift_segment import discover_sample_dirs
        cases_with_labels = []
        for case_dir in discover_sample_dirs(args.data_root, None):
            if (case_dir / "labels.nii.gz").exists():
                cases_with_labels.append(case_dir)
        
        if cases_with_labels:
            example_case = cases_with_labels[0]
            print(f"Using example case: {example_case.name}")
            
            # Run original mean shift
            cmd = [
                sys.executable, "meanshift_segment.py",
                "--src", str(args.data_root),
                "--dst", str(example_dir / "original"),
                "--prefix", example_case.name,
                "--copy_inputs"
            ]
            
            if run_command(cmd, f"Original Mean Shift on {example_case.name}"):
                success_count += 1
                print(f"✓ Example segmentation saved to {example_dir / 'original'}")
            else:
                print(f"✗ Example segmentation failed")
        else:
            print("No cases with ground truth found for examples")
            
    except Exception as e:
        print(f"Error generating examples: {e}")
    
    # Final summary
    print(f"\n{'='*60}")
    print("PIPELINE SUMMARY")
    print(f"{'='*60}")
    print(f"Completed {success_count}/{total_steps} steps successfully")
    
    if success_count == total_steps:
        print("🎉 All steps completed successfully!")
        print(f"\nResults are available in: {args.output_dir}")
        print("\nNext steps:")
        print("1. Check evaluation results in evaluation/summary_statistics.csv")
        print("2. View method comparison plots in evaluation/method_comparison.png")
        print("3. Examine example segmentations in examples/")
        
        if optimized_params_file.exists():
            print(f"4. Use optimized parameters from {optimized_params_file}")
        if tissue_params_file.exists():
            print(f"5. Use tissue-specific parameters from {tissue_params_file}")
            
    else:
        print("⚠️  Some steps failed. Check the output above for details.")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
